import {
  repository
} from '@loopback/repository';
import {
  get,
  param,
  post,
  response
} from '@loopback/rest';
import {StructuredResponse} from '../models';
import {DpReportRepository, NewEfSubcategory1Repository, NewEfSubcategory2Repository, NewEfSubcategory3Repository, NewEfSubcategory4Repository, QuantitativeSubmissionRepository, StructuredResponseRepository, SubmitDcfRepository} from '../repositories';

export class ManualController {
  constructor(
    @repository(SubmitDcfRepository)
    public submitDcfRepository: SubmitDcfRepository,
    @repository(NewEfSubcategory1Repository)
    public newEfSubcategory1Repository: NewEfSubcategory1Repository,
    @repository(NewEfSubcategory2Repository)
    public newEfSubcategory2Repository: NewEfSubcategory2Repository,
    @repository(NewEfSubcategory3Repository)
    public newEfSubcategory3Repository: NewEfSubcategory3Repository,
    @repository(NewEfSubcategory4Repository)
    public newEfSubcategory4Repository: NewEfSubcategory4Repository,
    @repository(DpReportRepository)
    public dpReportRepository: DpReportRepository,
    @repository(StructuredResponseRepository)
    private structuredResponseRepository: StructuredResponseRepository,
    @repository(QuantitativeSubmissionRepository)
    private quantitativeSubmissionsRepository: QuantitativeSubmissionRepository,
  ) { }

  @get('/manual-dpa-0136')

  async manualChange(

  ): Promise<any> {
    let submitDcf = await this.submitDcfRepository.find({where: {dcf: 10}});

    submitDcf = await Promise.all(submitDcf.map(async (data) => {
      const response = await Promise.all((data.response || []).map(async (info) => {
        const subData = await this.newEfSubcategory1Repository.findOne({where: {title: info.DPA0136.name}});
        info.DPA0136 = subData?.id;
        return info;
      }));

      await this.submitDcfRepository.updateById(data.id, {response: response})

      data.response = response;
      return data;
    }));

    return submitDcf;

  }

  @get('/manual-report-dpa-0136')

  async manualReportChange(

  ): Promise<any> {
    let dpReport = await this.dpReportRepository.find({where: {dcfId: 10, dpId: "DPA0136"}});

    dpReport = await Promise.all(dpReport.map(async (data) => {
      const value = data.value;
      const subData = await this.newEfSubcategory1Repository.findOne({where: {title: value.name}});
      if (subData?.id)
        await this.dpReportRepository.updateById(data.id, {type: "number", value: subData.id})
      data.value = value;
      data.type = "number";
      return data;
    }));

    return dpReport;

  }
  @get('/manual-dpa-0287')

  async manualChange2(

  ): Promise<any> {
    let submitDcf = await this.submitDcfRepository.find({where: {dcf: 16}});

    submitDcf = await Promise.all(submitDcf.map(async (data) => {
      const response = await Promise.all((data.response || []).map(async (info) => {
        const subData = await this.newEfSubcategory1Repository.findOne({where: {title: info.DPA0287.name}});
        info.DPA0287 = subData?.id;
        return info;
      }));

      await this.submitDcfRepository.updateById(data.id, {response: response})

      data.response = response;
      return data;
    }));

    return submitDcf;

  }

  @get('/manual-report-dpa-0287')

  async manualReportChange2(

  ): Promise<any> {
    let dpReport = await this.dpReportRepository.find({where: {dcfId: 16, dpId: "DPA0287"}});

    dpReport = await Promise.all(dpReport.map(async (data) => {
      const value = data.value;
      const subData = await this.newEfSubcategory1Repository.findOne({where: {title: value.name}});
      try {
        if (subData?.id)
          await this.dpReportRepository.updateById(data.id, {type: "number", value: subData.id})
      } catch (e) {
        console.log(e)
      }

      data.value = value;
      data.type = "number";
      return data;
    }));

    return dpReport;

  }

  @get('/manual-dpa-0130')

  async manualChange3(

  ): Promise<any> {
    let submitDcf = await this.submitDcfRepository.find({where: {dcf: 11}});

    submitDcf = await Promise.all(submitDcf.map(async (data) => {
      const response = await Promise.all((data.response || []).map(async (info) => {
        const subData1 = await this.newEfSubcategory1Repository.findOne({where: {title: info.DPA0130.name}});
        info.DPA0130 = subData1?.id || "";
        const subData2 = await this.newEfSubcategory2Repository.findOne({where: {title: info.DPA0131.name, newEfSubcategory1Id: subData1?.id}});
        info.DPA0131 = subData2?.id || "";
        const subData3 = await this.newEfSubcategory3Repository.findOne({where: {title: info.DPA0132.name, newEfSubcategory2Id: subData1?.id}});
        info.DPA0132 = subData3?.id || "";
        return info;
      }));

      await this.submitDcfRepository.updateById(data.id, {response: response})

      data.response = response;
      return data;
    }));

    return submitDcf;

  }

  @get('/manual-report-dpa-0130')

  async manualReport3Change(

  ): Promise<any> {
    const dpReport = await this.dpReportRepository.find({where: {dcfId: 11}});
    const formIds = dpReport.map(data => {return data.form_id})
    const uniqueFormIds = [...new Set(formIds)]
    for (const formId of uniqueFormIds) {
      const subData1 = await this.dpReportRepository.findOne({where: {dcfId: 11, form_id: formId, dpId: 'DPA0130'}})
      const subCat1 = await this.newEfSubcategory1Repository.findOne({where: {title: subData1?.value.name}})
      await this.dpReportRepository.updateById(subData1?.id, {value: subCat1?.id || "", type: "number"})

      const subData2 = await this.dpReportRepository.findOne({where: {dcfId: 11, form_id: formId, dpId: 'DPA0131'}})
      const subCat2 = await this.newEfSubcategory2Repository.findOne({where: {title: subData2?.value.name, newEfSubcategory1Id: subCat1?.id}})
      await this.dpReportRepository.updateById(subData2?.id, {value: subCat2?.id || "", type: "number"})

      const subData3 = await this.dpReportRepository.findOne({where: {dcfId: 11, form_id: formId, dpId: 'DPA0132'}})
      const subCat3 = await this.newEfSubcategory3Repository.findOne({where: {title: subData3?.value.name, newEfSubcategory2Id: subCat2?.id}})
      await this.dpReportRepository.updateById(subData3?.id, {value: subCat3?.id || "", type: "number"})
    }
    return uniqueFormIds;
  }

  //Adhi Reference - form 15 ||
  @post('/migrate-304')
  @response(200, {
    description: 'Data migration process initiated',
  })
  async migrateData() {
    //ref dcfId 15
    const submissions = await this.quantitativeSubmissionsRepository.find({
      where: {dcfId: 304, userProfileId: 94}, // ref not equal to 94
    });
    const dcfId = 304;
    if (!submissions.length) {

      console.log('No submissions found, inserted empty response.');
      return {message: 'No submissions found, inserted empty response.'};
    }
    await this.structuredResponseRepository.deleteAll({dcfId: dcfId, userProfileId: 94});
    const mappedData: Partial<StructuredResponse>[] = [];

    for (const submission of submissions) {
      if (submission.edit === 1 && submission.response && Array.isArray(submission.response) && submission.response.length === 0) {
        await this.structuredResponseRepository.create({
          dcfId,
          title: 'No submission for this reporting period',
          label: 'No submission for this reporting period',
          value: '-',
          formType: 2,
          submitDcfId: submission.id,
          isNull: true,
          created_on: submission.last_modified_on,
          created_by: submission.last_modified_by,
          uom: '-', userProfileId: 94, // ref submission.userProfileId
          reporting_period: submission.reporting_period
        });

        console.log(`No response found for submission ID: ${submission.id}, inserted empty response.`);
        continue;
      }

      for (const responseItem of submission?.response || []) {
        const DPA0130 = responseItem.DPA0130;
        const DPA0131 = responseItem.DPA0131;
        const DPA0132 = responseItem.DPA0132;
        const DPA0336 = responseItem.DPA0336;
        const DPA0131B = responseItem.DPA0131B;
        const DPA0131B1 = responseItem.DPA0131B1;
        const DPA0131B2 = responseItem.DPA0131B2;

        // Fetch Titles using Repository `findOne`
        const titleDPA0130 = await this.getTitleFromRepository(this.newEfSubcategory1Repository, DPA0130);
        const titleDPA0131 = await this.getTitleFromRepository(this.newEfSubcategory2Repository, DPA0131);
        const titleDPA0132 = await this.getTitleFromRepository(this.newEfSubcategory3Repository, DPA0132);

        let result: Partial<StructuredResponse> = {
          dcfId,
          formType: 2,
          dataType: 1,
          label: titleDPA0131,
          maskId: responseItem.id,
          submitDcfId: submission.id,
          userProfileId: 94, //ref form submission.userProfileId
          created_by: submission.last_modified_by,
          created_on: submission.last_modified_on,
          value: DPA0336,
          reporting_period: submission.reporting_period || [],
          attachment: responseItem.attachment || null
        };

        if ([533, 534].includes(DPA0131)) {
          if (DPA0131B === 1) {
            result = {
              ...result,
              title: `${titleDPA0130} > ${titleDPA0131}`,
              uom: DPA0131B2,
              additionalValue1: titleDPA0130,
              additionalValue2: DPA0131B1,
              subCategory1: DPA0130,
              subCategory2: DPA0131,
              parentId: DPA0131,
              efValue: responseItem.EF,
              uniqueId: `${DPA0130}-${DPA0131}`,
            };
          } else if (DPA0131B === 2) {
            result = {
              ...result,
              title: `${titleDPA0130} > ${titleDPA0131}`,
              uom: 'litres',
              subCategory1: DPA0130,
              subCategory2: DPA0131,
              additionalValue1: titleDPA0130,
              parentId: DPA0131,
              uniqueId: `${DPA0130}-${DPA0131}`,

            };
          }
        } else {
          result = {
            ...result,
            title: `${titleDPA0130} > ${titleDPA0131} > ${titleDPA0132}`,
            subCategory1: DPA0130,
            subCategory2: DPA0131,
            subCategory3: DPA0132,
            parentId: DPA0131,
            currentId: DPA0132,
            uniqueId: `${DPA0130}-${DPA0131}-${DPA0132}`,
            uom: titleDPA0132,
            additionalValue1: titleDPA0130,
          };
        }

        mappedData.push(result);
      }
    }

    for (const record of mappedData) {
      await this.structuredResponseRepository.create(record as StructuredResponse);
    }

    console.log('Data migration completed successfully.');
    return {message: 'Data migration completed successfully.'};
  }


  //Adhi Reference - form 15 ||
  @post('/migrate-15')
  @response(200, {
    description: 'Data migration process initiated',
  })
  async migrateData15() {
    const dcfId = 15;
    const submissions = await this.quantitativeSubmissionsRepository.find({
      where: {
        dcfId,
        userProfileId: {neq: 94},
      },
    });

    await this.structuredResponseRepository.deleteAll({dcfId, userProfileId: {neq: 94}});

    if (!submissions.length) {

      console.log('No submissions found, inserted empty response.');
      return {message: 'No submissions found, inserted empty response.'};
    }

    const fuel_type_f = [
      {title: "Diesel", id: 13, key: "4-13-51"},
      {title: "Petrol", id: 19, key: "4-19-75"},
      {title: "CNG", id: 2, key: "1-2-7"},
    ];
    // console.log(submissions, "submissions")
    const mappedData: Partial<StructuredResponse>[] = [];


    for (const submission of submissions) {
      if (submission.edit === 1 && submission.response && Array.isArray(submission.response) && submission.response.length === 0) {
        await this.structuredResponseRepository.create({
          dcfId,
          title: 'No submission for this reporting period',
          label: 'No submission for this reporting period',
          value: '-',
          formType: 2,
          submitDcfId: submission.id,
          isNull: true,
          created_on: submission.last_modified_on,
          created_by: submission.last_modified_by,
          uom: '-',
          userProfileId: submission.userProfileId, // ref submission.userProfileId
          reporting_period: submission.reporting_period
        });

        console.log(`No response found for submission ID: ${submission.id}, inserted empty response.`);
        continue;
      }

      for (const responseItem of submission?.response || []) {
        const DP_MODE = responseItem.DP_MODE;
        const DPA0140 = responseItem.DPA0140;
        const DPA0139 = responseItem.DPA0139;
        const DPA0141 = responseItem.DPA0141;
        const DPA0339 = responseItem.DPA0339;
        const DPA0143 = responseItem.DPA0143;
        const DPA0144 = responseItem.DPA0144;
        const DPA0142 = responseItem.DPA0142;

        const titleDPA0140 = await this.getTitleFromRepository(this.newEfSubcategory1Repository, DPA0140);
        const titleDPA0139 = await this.getTitleFromRepository(this.newEfSubcategory2Repository, DPA0139);
        const titleDPA0141 = await this.getTitleFromRepository(this.newEfSubcategory3Repository, DPA0141);
        const titleDPA0339 = await this.getTitleFromRepository(this.newEfSubcategory4Repository, DPA0339);

        let title = '';
        let label = '';
        let uom = '';
        let key = '';
        let subCategory1, subCategory2, subCategory3, subCategory4;
        let parentId, currentId;
        let value;

        if (DP_MODE) {
          key = `${DPA0140}-${DPA0139}-${DPA0141}-${DPA0339}`;
          title = `by Distance > ${titleDPA0139} > ${titleDPA0141}`;
          label = title;
          uom = titleDPA0339 || 'Distance';
          value = DPA0144;

          subCategory1 = DPA0140;
          subCategory2 = DPA0139;
          subCategory3 = DPA0141;
          subCategory4 = DPA0339;

          parentId = DPA0141;
          currentId = DPA0339;
        } else {
          // Fuel-based form
          const fuel = fuel_type_f.find(i => i.id === DPA0141);
          key = fuel?.key || '';
          title = `by Fuel > ${fuel?.title}`;
          label = title;
          uom = 'Litres';
          value = DPA0143;

          const keyParts = fuel?.key?.split('-') || [];
          subCategory1 = Number(keyParts[0]);
          subCategory2 = Number(keyParts[1]);
          subCategory3 = Number(keyParts[2]);
          subCategory4 = DPA0339;

          parentId = DPA0141;
          currentId = Number(keyParts[2]);
        }

        const result: Partial<StructuredResponse> = {
          dcfId,
          formType: 2,
          dataType: 1,
          title,
          label,
          value,
          subCategory1,
          subCategory2,
          subCategory3,
          subCategory4,
          parentId,
          currentId,
          uom,
          uniqueId: key,
          maskId: responseItem.id,
          submitDcfId: submission.id,
          userProfileId: submission.userProfileId,
          created_by: submission.last_modified_by,
          created_on: submission.last_modified_on,
          reporting_period: submission.reporting_period || [],
          attachment: responseItem.attachment || null,
          date: DP_MODE ? null : DPA0142
        };

        mappedData.push(result);
      }
    }


    // return mappedData;
    // console.log(mappedData, "mappedData")
    for (const record of mappedData) {
      await this.structuredResponseRepository.create(record as StructuredResponse);
    }

    // console.log('Data migration completed successfully.');
    return {message: 'Data migration completed successfully.'};
  }


  //Adhi Reference - form 15 ||
  @post('/migrate-257')
  @response(200, {
    description: 'Data migration process initiated',
  })
  async migrateData257() {
    //ref dcfId 15
    const submissions = await this.quantitativeSubmissionsRepository.find({
      where: {dcfId: 257, userProfileId: {neq: 94}}, // ref not equal to 94
    });
    const dcfId = 257;
    if (!submissions.length) {

      console.log('No submissions found, inserted empty response.');
      return {message: 'No submissions found, inserted empty response.'};
    }
    await this.structuredResponseRepository.deleteAll({dcfId: 257, userProfileId: {neq: 94}});

    const mappedData: Partial<StructuredResponse>[] = [];

    for (const submission of submissions) {
      if (submission.edit === 1 && submission.response && Array.isArray(submission.response) && submission.response.length === 0) {
        await this.structuredResponseRepository.create({
          dcfId,
          title: 'No submission for this reporting period',
          label: 'No submission for this reporting period',
          value: '-',
          formType: 2,
          submitDcfId: submission.id,
          isNull: true,
          created_on: submission.last_modified_on,
          created_by: submission.last_modified_by,
          uom: '-', userProfileId: submission.userProfileId, // ref submission.userProfileId
          reporting_period: submission.reporting_period
        });

        console.log(`No response found for submission ID: ${submission.id}, inserted empty response.`);
        continue;
      }

      for (const responseItem of submission?.response || []) {
        const DPAN095 = responseItem.DPAN095;
        const DPAN096 = responseItem.DPAN096;
        const DPAN099 = responseItem.DPAN099;
        const DPAN098 = responseItem.DPAN098;


        // Fetch Titles using Repository `findOne`
        const titleDPAN095 = await this.getTitleFromRepository(this.newEfSubcategory1Repository, DPAN095);
        const titleDPAN096 = await this.getTitleFromRepository(this.newEfSubcategory2Repository, DPAN096);
        const titleDPAN099 = await this.getTitleFromRepository(this.newEfSubcategory3Repository, DPAN099);

        let result: Partial<StructuredResponse> = {
          dcfId,
          formType: 2,
          dataType: 1,
          label: `${titleDPAN095} > ${titleDPAN096}`,
          maskId: responseItem.id,
          submitDcfId: submission.id,
          userProfileId: submission.userProfileId, //ref form submission.userProfileId
          created_by: submission.last_modified_by,
          created_on: submission.last_modified_on,
          value: DPAN098,
          reporting_period: submission.reporting_period || [],
          attachment: responseItem.attachment || null
        };


        result = {
          ...result,
          title: `${titleDPAN095} > ${titleDPAN096} > ${titleDPAN099}`,
          subCategory1: DPAN095,
          subCategory2: DPAN096,
          subCategory3: DPAN099,
          parentId: DPAN096,
          currentId: DPAN099,
          uniqueId: `${DPAN095}-${DPAN096}-${DPAN099}`,
          uom: titleDPAN099,

        };


        mappedData.push(result);
      }
    }

    for (const record of mappedData) {
      await this.structuredResponseRepository.create(record as StructuredResponse);
    }

    console.log('Data migration completed successfully.');
    return {message: 'Data migration completed successfully.'};
  }
  /**
   * Fetch the title for a given DPA ID from the appropriate repository.
   */
  private async getTitleFromRepository(repo: any, dpaId: number): Promise<string> {
    if (!dpaId) return '-';

    const record = await repo.findOne({where: {id: dpaId}});
    return record?.title || `DPA-${dpaId}`; // Return "DPA-ID" if not found
  }

  /**
   * Endpoint to check the migrated data.
   */
  @get('/migrated-data/{dcfId}')
  @response(200, {
    description: 'List of migrated data in StructuredResponse',
  })
  async getMigratedData(@param.path.number('dcfId') dcfId: number) {
    const migratedData = await this.structuredResponseRepository.find({
      where: {dcfId: dcfId},
    });

    return migratedData.length
      ? migratedData
      : {message: 'No migrated data found for this DCF ID.'};
  }
  // DONE
  @post('/migrate-305')
  @response(200, {
    description: 'Data migration process for DCF 305 initiated',
  })
  async migrateData305() {
    const dcfId = 305;
    console.log(`Starting data migration for DCF ID: ${dcfId}`);

    const submissions = await this.quantitativeSubmissionsRepository.find({
      where: {dcfId: dcfId, userProfileId: 94},
    });

    if (!submissions.length) {


      console.log('No submissions found, inserted empty response.');
      return {message: 'No submissions found, inserted empty response.'};
    }
    await this.structuredResponseRepository.deleteAll({dcfId: dcfId, userProfileId: 94});
    const mappedData: Partial<StructuredResponse>[] = [];

    for (const submission of submissions) {
      if (submission.edit === 1 && submission.response && Array.isArray(submission.response) && submission.response.length === 0) {
        await this.structuredResponseRepository.create({
          dcfId,
          title: 'No submission for this reporting period',
          label: 'No submission for this reporting period',
          value: '-',
          formType: 2,
          submitDcfId: submission.id,
          isNull: true,
          created_on: submission.last_modified_on,
          created_by: submission.last_modified_by,
          uom: '-', userProfileId: 94,
          reporting_period: submission.reporting_period
        });

        console.log(`No response found for submission ID: ${submission.id}, inserted empty response.`);
        continue;
      }

      for (const responseItem of submission?.response || []) {
        // Extract necessary values
        const DPA0136 = responseItem.DPA0136;
        const DPA0138 = responseItem.DPA0138;
        const DPA0136B = responseItem.DPA0136B;
        const DPA0136A = responseItem.DPA0136A; // Needed if DPA0136 === 338

        if (!DPA0136) {
          console.log(`Skipping response in submission ID ${submission.id}, missing DPA0136.`);
          continue;
        }

        // Fetch Title for DPA0136 using Repository `findOne`
        const titleDPA0136 = await this.getTitle2FromRepository(this.newEfSubcategory1Repository, DPA0136);
        const subCategory2Data = await this.newEfSubcategory2Repository.findOne({where: {newEfSubcategory1Id: DPA0136}});

        let currentId = null;
        if (subCategory2Data && subCategory2Data.id) {
          currentId = subCategory2Data.id;
        }
        if ((DPA0136 !== 346 && currentId) || DPA0136 === 346) {
          let result: Partial<StructuredResponse> = {
            dcfId,
            formType: 2,
            dataType: 1,
            label: titleDPA0136,
            maskId: responseItem.id,
            submitDcfId: submission.id,
            userProfileId: 94, // Dynamic user profile
            value: DPA0138,
            reporting_period: submission.reporting_period || [],
            title: titleDPA0136 + ">kg",
            subCategory1: DPA0136,
            subCategory2: currentId,
            parentId: DPA0136,
            uniqueId: currentId ? `${DPA0136}-${currentId}` : `${DPA0136}`,
            uom: 'kg',
            created_by: submission.last_modified_by,
            created_on: submission.last_modified_on
          };

          if (DPA0136 === 346) {
            result = {
              ...result,
              additionalValue1: DPA0136A, // Title of DPA0136A
              efValue: DPA0136B, // Adding efValue as per the requirement
            };
          }
          mappedData.push(result);
        }

      }
    }

    for (const record of mappedData) {
      await this.structuredResponseRepository.create(record as StructuredResponse);
    }

    console.log('Data migration for DCF 305 completed successfully.');
    return {message: 'Data migration for DCF 305 completed successfully.'};
  }



  @post('/migrate-dcf-16')
  @response(200, {
    description: 'Data migration process for DCF 16 initiated',
  })
  async migrateDcf16() {
    const dcfId = 16;
    const submissions = await this.quantitativeSubmissionsRepository.find({
      where: {
        dcfId: dcfId,
        userProfileId: {neq: 94}
      }
    });

    await this.structuredResponseRepository.deleteAll({dcfId: dcfId, userProfileId: {neq: 94}});
    const mappedData: any[] = [];

    const category = await this.newEfSubcategory1Repository.find({
      where: {newEfCategoryId: 11}
    });

    for (const submission of submissions) {
      if (submission.edit === 1 && submission.response && Array.isArray(submission.response) && submission.response.length === 0) {
        await this.structuredResponseRepository.create({
          dcfId,
          title: 'No submission for this reporting period',
          label: 'No submission for this reporting period',
          value: '-',
          formType: 2,
          submitDcfId: submission.id,
          isNull: true,
          created_on: submission.last_modified_on,
          created_by: submission.last_modified_by,
          uom: '-',
          userProfileId: submission.userProfileId,
          reporting_period: submission.reporting_period
        });

        console.log(`No response found for submission ID: ${submission.id}, inserted empty response.`);
        continue;
      }

      const data = submission.response;
      if (!data || !Array.isArray(data)) continue;

      let mappedDataCategory = category.map((x: any) => ({
        key: x.id.toString(),
        currentId: x.id,
        parentId: x.id,
        title1: x.title
      })).filter((i: any) => i);

      console.log(data);
      let result = [];

      for (const item of data) {
        let index = mappedDataCategory.find((i: any) => i.key === (item['DPA0287']?.toString()))

        console.log(index);

        if (index) {
          result.push({
            title: index.title1 + " > " + item.DPA0286,
            label: index.title1 + " > " + item.DPA0286,
            value: parseFloat((item.DPA0288 * item.DPA0289).toString()),
            subCategory1: index.parentId,
            currentId: index.currentId,
            parentId: index.parentId,
            formType: 2,
            dataType: 1,
            isNull: false,
            attachment: item.attachment,
            uom: 'USD',
            uniqueId: index.key + "",
            maskId: item.id,
            reportedDate: item.DPA0285,
            created_by: submission.last_modified_by,
            created_on: submission.last_modified_on
          });
        }
      }

      console.log(result, 'RESULT');

      for (const responseItem of result) {
        mappedData.push({
          dcfId: dcfId,
          userProfileId: submission.userProfileId,
          reporting_period: submission.reporting_period,
          submitDcfId: submission.id,
          ...responseItem
        });
      }
    }

    if (mappedData.length > 0) {
      await this.structuredResponseRepository.createAll(mappedData);
    }

    return {message: `Migration completed for DCF ${dcfId}. Processed ${mappedData.length} records.`};
  }

  @post('/migrate-dcf-282')
  @response(200, {
    description: 'Data migration process for DCF 282 initiated',
  })
  async migrateDcf282() {
    const dcfId = 282;
    const submissions = await this.quantitativeSubmissionsRepository.find({
      where: {
        dcfId: dcfId,
        userProfileId: {neq: 94}
      }
    });

    await this.structuredResponseRepository.deleteAll({dcfId: dcfId, userProfileId: {neq: 94}});
    const mappedData: any[] = [];

    const category = await this.newEfSubcategory1Repository.find({
      where: {newEfCategoryId: 11}
    });

    for (const submission of submissions) {
      if (submission.edit === 1 && submission.response && Array.isArray(submission.response) && submission.response.length === 0) {
        await this.structuredResponseRepository.create({
          dcfId,
          title: 'No submission for this reporting period',
          label: 'No submission for this reporting period',
          value: '-',
          formType: 2,
          submitDcfId: submission.id,
          isNull: true,
          created_on: submission.last_modified_on,
          created_by: submission.last_modified_by,
          uom: '-',
          userProfileId: submission.userProfileId,
          reporting_period: submission.reporting_period
        });

        console.log(`No response found for submission ID: ${submission.id}, inserted empty response.`);
        continue;
      }

      const data = submission.response;
      if (!data || !Array.isArray(data)) continue;

      let mappedDataCategory = category.map((x: any) => ({
        key: x.id.toString(),
        currentId: x.id,
        parentId: x.id,
        title1: x.title
      })).filter((i: any) => i);

      console.log(mappedDataCategory);
      let result = [];

      for (const item of data) {
        let index = mappedDataCategory.find((i: any) => i.key === (item['DPAN1129']?.toString()))
        console.log(index, mappedDataCategory);

        if (index) {
          result.push({
            title: index.title1 + " > " + item.DPAN1130,
            label: index.title1 + " > " + item.DPAN1130,
            value: parseFloat((item.DPAN1127 * item.DPAN1128).toString()),
            subCategory1: index.parentId,
            currentId: index.currentId,
            parentId: index.parentId,
            formType: 2,
            dataType: 1,
            isNull: false,
            attachment: item.attachment,
            uom: 'USD',
            uniqueId: index.key + "",
            maskId: item.id,
            reportedDate: item.DPAN1131,
            created_by: submission.last_modified_by,
            created_on: submission.last_modified_on,
          });
        }
      }

      console.log(result, 'RESULT');

      for (const responseItem of result) {
        mappedData.push({
          dcfId: dcfId,
          userProfileId: submission.userProfileId,
          reporting_period: submission.reporting_period,
          submitDcfId: submission.id,
          ...responseItem
        });
      }
    }

    if (mappedData.length > 0) {
      await this.structuredResponseRepository.createAll(mappedData);
    }

    return {message: `Migration completed for DCF ${dcfId}. Processed ${mappedData.length} records.`};
  }

  @post('/migrate-10')
  @response(200, {
    description: 'Data migration process for DCF 305 initiated',
  })
  async migrateData10() {
    const dcfId = 10;
    console.log(`Starting data migration for DCF ID: ${dcfId}`);

    const submissions = await this.quantitativeSubmissionsRepository.find({
      where: {dcfId: dcfId, userProfileId: {neq: 94}},
    });

    if (!submissions.length) {


      console.log('No submissions found, inserted empty response.');
      return {message: 'No submissions found, inserted empty response.'};
    }
    await this.structuredResponseRepository.deleteAll({dcfId: dcfId, userProfileId: {neq: 94}});

    const mappedData: Partial<StructuredResponse>[] = [];

    for (const submission of submissions) {
      if (submission.edit === 1 && submission.response && Array.isArray(submission.response) && submission.response.length === 0) {
        await this.structuredResponseRepository.create({
          dcfId,
          title: 'No submission for this reporting period',
          label: 'No submission for this reporting period',
          value: '-',
          formType: 2,
          submitDcfId: submission.id,
          isNull: true,
          created_on: submission.last_modified_on,
          created_by: submission.last_modified_by,
          uom: '-', userProfileId: submission.userProfileId,
          reporting_period: submission.reporting_period
        });

        console.log(`No response found for submission ID: ${submission.id}, inserted empty response.`);
        continue;
      }

      for (const responseItem of submission?.response || []) {
        // Extract necessary values
        const DPA0136 = responseItem.DPA0136;
        const DPA0138 = responseItem.DPA0138;
        const DPA0137 = responseItem.DPA0137;
        const DPA0136B = responseItem.DPA0136B;
        const DPA0136A = responseItem.DPA0136A; // Needed if DPA0136 === 338


        if (!DPA0136) {
          console.log(`Skipping response in submission ID ${submission.id}, missing DPA0136.`);
          continue;
        }

        // Fetch Title for DPA0136 using Repository `findOne`
        const titleDPA0136 = await this.getTitle2FromRepository(this.newEfSubcategory1Repository, DPA0136);
        const subCategory2Data = await this.newEfSubcategory2Repository.findOne({where: {newEfSubcategory1Id: DPA0136}});

        let currentId = null;
        if (subCategory2Data && subCategory2Data.id) {
          currentId = subCategory2Data.id;
        }


        if ((DPA0136 !== 346 && currentId) || DPA0136 === 346) {
          const result: Partial<StructuredResponse> = {
            dcfId,
            formType: 2,
            dataType: 1,
            label: titleDPA0136,
            maskId: responseItem.id,
            submitDcfId: submission.id,
            userProfileId: submission.userProfileId, // Dynamic user profile
            value: DPA0138,
            reporting_period: submission.reporting_period || [],
            title: titleDPA0136 + ">kg",
            subCategory1: DPA0136,
            subCategory2: currentId,
            parentId: DPA0136,
            currentId: currentId || null,
            uniqueId: currentId ? `${DPA0136}-${currentId}` : `${DPA0136}`,
            uom: 'kg',
            created_by: submission.last_modified_by,
            created_on: submission.last_modified_on,
            reportedDate: DPA0137
          };




          mappedData.push(result);
        }
      }
    }


    // console.log(mappedData, "mappedData")
    for (const record of mappedData) {
      await this.structuredResponseRepository.create(record as StructuredResponse);
    }

    // console.log('Data migration for DCF 10 completed successfully.');
    return {message: 'Data migration for DCF 10 completed successfully.'};
  }

  /**
   * Fetch the title for a given DPA ID from the appropriate repository.
   */
  private async getTitle2FromRepository(repo: any, dpaId: number): Promise<string> {
    if (!dpaId) return '-';

    const record = await repo.findOne({where: {id: dpaId}});
    return record?.title || `DPA-${dpaId}`; // Return "DPA-ID" if not found
  }

  /**
   * Endpoint to check the migrated data.
   */
  @get('/migrated-data/305')
  @response(200, {
    description: 'List of migrated data in StructuredResponse for DCF 305',
  })
  async getMigratedData305() {
    const dcfId = 305;
    const migratedData = await this.structuredResponseRepository.find({
      where: {dcfId: dcfId},
    });

    return migratedData.length
      ? migratedData
      : {message: 'No migrated data found for this DCF ID.'};
  }


  @post('/migrate-287')
  @response(200, {
    description: 'Data migration process for DCF 287 initiated',
  })
  async migrateData287() {
    const dcfId = 287;
    console.log(`Starting data migration for DCF ID: ${dcfId}`);

    const submissions = await this.quantitativeSubmissionsRepository.find({
      where: {dcfId: dcfId, userProfileId: 94},
    });

    if (!submissions.length) {


      console.log('No submissions found, inserted empty response.');
      return {message: 'No submissions found, inserted empty response.'};
    }
    await this.structuredResponseRepository.deleteAll({dcfId: dcfId, userProfileId: 94});
    const mappedData: Partial<StructuredResponse>[] = [];

    for (const submission of submissions) {
      if (submission.edit === 1 && submission.response && Array.isArray(submission.response) && submission.response.length === 0) {
        await this.structuredResponseRepository.create({
          dcfId,
          title: 'No submission for this reporting period',
          label: 'No submission for this reporting period',
          value: '-',
          formType: 2,
          submitDcfId: submission.id,
          isNull: true,
          created_on: submission.last_modified_on,
          created_by: submission.last_modified_by,
          uom: '-', userProfileId: 94,
          reporting_period: submission.reporting_period
        });

        console.log(`No response found for submission ID: ${submission.id}, inserted empty response.`);
        continue;
      }

      for (const responseItem of submission?.response || []) {
        // Extract necessary values
        const DPAN095 = responseItem.DPAN095;
        const DPAN095A = responseItem.DPAN095A;
        const DPAN095B = responseItem.DPAN095B;
        const DPAN098A = responseItem.DPAN098A;
        const DPAN098B = responseItem.DPAN098B;

        if (!DPAN095) {
          console.log(`Skipping response in submission ID ${submission.id}, missing DPAN095.`);
          continue;
        }

        // Fetch Titles using Repository `findOne`
        const titleDPAN095 = await this.getTitle3FromRepository(this.newEfSubcategory1Repository, DPAN095);
        const titleDPAN095A = await this.getTitle3FromRepository(this.newEfSubcategory2Repository, DPAN095A);
        const titleDPAN095B = await this.getTitle3FromRepository(this.newEfSubcategory3Repository, DPAN095B);

        let result: Partial<StructuredResponse> = {
          dcfId,
          formType: 2,
          dataType: 1,
          label: titleDPAN095,
          maskId: responseItem.id,
          submitDcfId: submission.id,
          userProfileId: 94, // Dynamic user profile
          reporting_period: submission.reporting_period || [],
          uom: 'kWh', // Constant unit
          created_on: submission.last_modified_on,
          created_by: submission.last_modified_by,
        };

        if (DPAN095 === 335) {
          result = {
            ...result,
            title: titleDPAN095 + ">kWh", // Title of DPAN095
            value: DPAN098A,
            subCategory1: DPAN095,
            parentId: DPAN095,
            uniqueId: `${DPAN095}`,
          };
        } else {
          result = {
            ...result,
            title: `${titleDPAN095} > ${titleDPAN095A} > ${titleDPAN095B}`,
            value: DPAN098B,
            subCategory1: DPAN095,
            subCategory2: DPAN095A,
            subCategory3: DPAN095B,
            parentId: DPAN095A,
            currentId: DPAN095B,
            uniqueId: `${DPAN095}-${DPAN095A}-${DPAN095B}`,
            additionalValue1: titleDPAN095A,
            additionalValue2: titleDPAN095B,
          };
        }

        mappedData.push(result);
      }
    }

    for (const record of mappedData) {
      await this.structuredResponseRepository.create(record as StructuredResponse);
    }

    console.log('Data migration for DCF 287 completed successfully.');
    return {message: 'Data migration for DCF 287 completed successfully.'};
  }

  /**
   * Fetch the title for a given DPAN ID from the appropriate repository.
   */
  private async getTitle3FromRepository(repo: any, dpaId: number): Promise<string> {
    if (!dpaId) return '-';

    const record = await repo.findOne({where: {id: dpaId}});
    return record?.title || `DPA-${dpaId}`; // Return "DPA-ID" if not found
  }
  /**
   * Endpoint to check the migrated data.
   */
  @get('/migrated-data/287')
  @response(200, {
    description: 'List of migrated data in StructuredResponse for DCF 287',
  })
  async getMigratedData287() {
    const dcfId = 287;
    const migratedData = await this.structuredResponseRepository.find({
      where: {dcfId: dcfId},
    });

    return migratedData.length
      ? migratedData
      : {message: 'No migrated data found for this DCF ID.'};
  }

  @post('/migrate-264')
  @response(200, {
    description: 'Data migration process for DCF 264 initiated',
  })
  async migrateData264() {
    const dcfId = 264;
    console.log(`Starting data migration for DCF ID: ${dcfId}`);

    const submissions = await this.quantitativeSubmissionsRepository.find({
      where: {dcfId: dcfId, userProfileId: 94},
    });

    if (!submissions.length) {
      return {message: 'No submissions found, inserted empty response.'};
    }
    await this.structuredResponseRepository.deleteAll({dcfId: dcfId, userProfileId: 94});
    const mappedData: Partial<StructuredResponse>[] = [];
    const ids: any = [];
    for (const submission of submissions) {

      if (submission.edit === 1 && submission.response && Array.isArray(submission.response) && submission.response.length === 0) {
        await this.structuredResponseRepository.create({
          dcfId,
          title: 'No submission for this reporting period',
          label: 'No submission for this reporting period',
          value: '-',
          formType: 1,
          isNull: true,
          uom: '-',
          userProfileId: 94, submitDcfId: submission.id,
          reporting_period: submission.reporting_period || [],
          created_on: submission.last_modified_on,
          created_by: submission.last_modified_by,
          isManualForm: true
        });
        continue;
      }

      for (const element of submission?.response || []) {

        if (element.type === 'number') {
          const cleanLabel = element.label
            ?.replace(/<\/?[^>]+(>|$)/g, '') // Removes all HTML tags
            ?.replace(/\n/g, ' ') // Removes newlines
            ?.replace(/&nbsp;/g, ' ') // Replaces HTML space entity
            ?.replace("&amp;", "&") || ''; // Replaces &amp; with &

          const result: Partial<StructuredResponse> = {
            dcfId,
            maskId: element.name,
            uniqueId: element.name,
            uom: 'kWh',
            userProfileId: 94, // Admin profile ID (Modify if dynamic)
            isManualForm: true,
            valueType: (element.value != null) ? typeof element.value : null,
            value: element.value,
            title: `${cleanLabel} > kWh`, // Keep title as is (HTML allowed)
            label: cleanLabel, // Strip HTML from label
            formType: 1,
            dataType: 1,
            submitDcfId: submission.id,
            created_on: submission.last_modified_on,
            created_by: submission.last_modified_by,
            reporting_period: submission.reporting_period || [],
          };

          mappedData.push(result);


        }


      }
    }

    for (const record of mappedData) {
      await this.structuredResponseRepository.create(record as StructuredResponse);
    }

    console.log('Data migration for DCF 264 completed successfully.');
    return {message: 'Data migration for DCF 264 completed successfully.'};
  }


  /**
   * Migration for DCF 285
   */
  @post('/migrate-285')
  @response(200, {
    description: 'Data migration process for DCF 285 initiated',
  })
  async migrateData285() {
    const dcfId = 285;
    console.log(`Starting data migration for DCF ID: ${dcfId}`);

    const DPAN0048Options = [
      {name: "Self Generated Water", id: 6},
      {name: "Third-Party Water", id: 5},
    ];

    const DPAN1158Options = [
      {name: "Groundwater Wells Operated", id: 1},
      {name: "Effluent/ Sewage Treatment Recycle", id: 2},
      {name: 'Rainwater Harvesting', id: 3},
      {name: 'Others', id: 99}
    ];

    const DPAN1200Options = [
      {name: "Surface water", id: 1},
      {name: "Ground Water", id: 2},
      {name: "Sea Water", id: 3},
      {name: 'Imported Water from Industrial District', id: 8},
      {name: 'Unknown', id: 9}
    ];

    const submissions = await this.quantitativeSubmissionsRepository.find({
      where: {dcfId: dcfId, userProfileId: 94},
    });

    if (!submissions.length) {

      return {message: 'No submissions found, inserted empty response.'};
    }
    await this.structuredResponseRepository.deleteAll({dcfId: dcfId, userProfileId: 94});
    const mappedData: Partial<StructuredResponse>[] = [];

    for (const submission of submissions) {
      if (submission.edit === 1 && submission.response && Array.isArray(submission.response) && submission.response.length === 0) {
        await this.structuredResponseRepository.create({
          dcfId,
          title: 'No submission for this reporting period',
          label: 'No submission for this reporting period',
          value: '-',
          formType: 2,
          submitDcfId: submission.id,
          isNull: true,
          created_on: submission.last_modified_on,
          created_by: submission.last_modified_by,
          uom: '-', userProfileId: 94,
          reporting_period: submission.reporting_period
        });
        continue;
      }

      for (const item of submission?.response || []) {
        const isSelfGenerated = item.DPAN0048 === 6;
        const parentOption = DPAN0048Options.find(opt => opt.id === item.DPAN0048);
        const currentOption = isSelfGenerated
          ? DPAN1158Options.find(opt => opt.id === item.DPAN1158)
          : DPAN1200Options.find(opt => opt.id === item.DPAN1200);

        if (!parentOption || !currentOption) continue;

        const result: Partial<StructuredResponse> = {
          dcfId,
          title: `${parentOption.name} > ${currentOption.name} > m3`,
          label: parentOption.name,
          value: isSelfGenerated ? item.DPAN1159 || 0 : item.DPAN1161 || 0,
          currentId: isSelfGenerated ? item.DPAN1158 : item.DPAN1200,
          parentId: item.DPAN0048,
          formType: 2,
          dataType: 2,
          isNull: false,
          created_on: submission.last_modified_on,
          created_by: submission.last_modified_by,
          attachment: item.attachment || null,
          uom: 'm3',
          uniqueId: `${item.DPAN0048}-${isSelfGenerated ? item.DPAN1158 : item.DPAN1200}`,
          maskId: item.id,
          userProfileId: 94,
          reporting_period: submission.reporting_period,
          additionalValue1: currentOption.name,
          submitDcfId: submission.id
        };

        mappedData.push(result);
      }
    }

    for (const record of mappedData) {
      await this.structuredResponseRepository.create(record as StructuredResponse);
    }

    console.log('Data migration for DCF 285 completed successfully.');
    return {message: 'Data migration for DCF 285 completed successfully.'};
  }
  @post('/migrate-310')
  @response(200, {
    description: 'Data migration process for DCF 285 initiated',
  })
  async migrateData310() {
    const dcfId = 310;
    console.log(`Starting data migration for DCF ID: ${dcfId}`);


    const submissions = await this.quantitativeSubmissionsRepository.find({
      where: {dcfId: dcfId, userProfileId: 291},
    });
    await this.structuredResponseRepository.deleteAll({dcfId: dcfId, userProfileId: 291});
    if (!submissions.length) {

      return {message: 'No submissions found, inserted empty response.'};
    }

    const mappedData: Partial<StructuredResponse>[] = [];


    for (const submission of submissions) {
      if (submission.edit === 1 && submission.response && Array.isArray(submission.response) && submission.response.length === 0) {
        await this.structuredResponseRepository.create({
          dcfId,
          title: 'No submission for this reporting period',
          label: 'No submission for this reporting period',
          value: '-',
          formType: 2,
          submitDcfId: submission.id,
          isNull: true,
          created_on: submission.last_modified_on,
          created_by: submission.last_modified_by,
          uom: '-', userProfileId: 291,
          reporting_period: submission.reporting_period
        });

        console.log(`No response found for submission ID: ${submission.id}, inserted empty response.`);
        continue;
      }

      for (const responseItem of submission?.response || []) {
        const DPA0130 = responseItem.DPA0130;
        const DPA0131 = responseItem.DPA0131;
        const DPA0132 = responseItem.DPA0132;

        // Fetch Titles using Repository `findOne`
        const titleDPA0130 = await this.getTitleFromRepository(this.newEfSubcategory1Repository, DPA0130);
        const titleDPA0131 = await this.getTitleFromRepository(this.newEfSubcategory2Repository, DPA0131);
        const titleDPA0132 = await this.getTitleFromRepository(this.newEfSubcategory3Repository, DPA0132);

        const result: Partial<StructuredResponse> = {
          title: `${titleDPA0130} > ${titleDPA0131}> ${titleDPA0132}`,
          dcfId,
          formType: 2,
          dataType: 1,
          label: titleDPA0131,
          maskId: responseItem.id,
          submitDcfId: submission.id,
          userProfileId: 291,
          created_by: submission.last_modified_by,
          created_on: submission.last_modified_on,
          currentId: DPA0132,
          uom: titleDPA0132,
          uniqueId: `${DPA0130}-${DPA0131}-${DPA0132}`,
          parentId: DPA0131,
          subCategory1: DPA0130,
          subCategory2: DPA0131,
          subCategory3: DPA0132,
          value: parseFloat(responseItem?.DPA0336 || null),
          reporting_period: submission.reporting_period || [],
          attachment: responseItem.attachment || null
        };



        mappedData.push(result);
      }
    }

    for (const record of mappedData) {
      await this.structuredResponseRepository.create(record as StructuredResponse);
    }

    console.log('Data migration for DCF 285 completed successfully.');
    return {message: 'Data migration for DCF 285 completed successfully.'};
  }

  @post('/migrate-311')
  @response(200, {
    description: 'Data migration process for DCF 305 initiated',
  })
  async migrateData311() {
    const dcfId = 311;
    console.log(`Starting data migration for DCF ID: ${dcfId}`);

    const submissions = await this.quantitativeSubmissionsRepository.find({
      where: {dcfId: dcfId, userProfileId: 291},
    });
    await this.structuredResponseRepository.deleteAll({dcfId: dcfId, userProfileId: 291});

    if (!submissions.length) {


      console.log('No submissions found, inserted empty response.');
      return {message: 'No submissions found, inserted empty response.'};
    }

    const mappedData: Partial<StructuredResponse>[] = [];

    for (const submission of submissions) {
      if (submission.edit === 1 && submission.response && Array.isArray(submission.response) && submission.response.length === 0) {
        await this.structuredResponseRepository.create({
          dcfId,
          title: 'No submission for this reporting period',
          label: 'No submission for this reporting period',
          value: '-',
          formType: 2,
          submitDcfId: submission.id,
          isNull: true,
          created_on: submission.last_modified_on,
          created_by: submission.last_modified_by,
          uom: '-', userProfileId: 291,
          reporting_period: submission.reporting_period
        });

        console.log(`No response found for submission ID: ${submission.id}, inserted empty response.`);
        continue;
      }

      for (const responseItem of submission?.response || []) {
        // Extract necessary values
        const DPAW0002 = responseItem.DPAW0002;
        const DPA0141 = responseItem.DPA0141;
        const DPAW0007 = responseItem.DPAW0007;

        if (!DPAW0002 || !DPA0141 || !DPAW0007) {
          console.log(`Skipping response in submission ID ${submission.id}, missing DPA0136.`);
          continue;
        }

        // Fetch Title for DPA0136 using Repository `findOne`
        const titleDPAW0002 = await this.getTitleFromRepository(this.newEfSubcategory2Repository, DPAW0002);
        const titleDPA0141 = await this.getTitleFromRepository(this.newEfSubcategory3Repository, DPA0141);
        const titleDPAW0007 = await this.getTitleFromRepository(this.newEfSubcategory4Repository, DPAW0007);

        const result: Partial<StructuredResponse> = {
          dcfId,
          formType: 2,
          dataType: 1,
          label: (DPAW0002 === 557 ? titleDPAW0002 + "/" + responseItem.DPAW0002A : titleDPAW0002) + ">" + titleDPA0141,
          maskId: responseItem.id,
          submitDcfId: submission.id,
          userProfileId: 291, // Dynamic user profile
          value: parseFloat(responseItem.DPA0143),
          reporting_period: submission.reporting_period || [],
          title: (DPAW0002 === 557 ? titleDPAW0002 + "/" + responseItem.DPAW0002A : titleDPAW0002) + ">" + titleDPA0141 + ">" + titleDPAW0007,
          subCategory1: 338,
          subCategory2: DPAW0002,
          subCategory3: DPA0141,
          subCategory4: DPAW0007,
          parentId: DPA0141,
          currentId: DPAW0007,
          uniqueId: `338-${DPAW0002}-${DPA0141}-${DPAW0007}`,
          uom: titleDPAW0007,
          created_by: submission.last_modified_by,
          created_on: submission.last_modified_on
        };



        mappedData.push(result);
      }
    }

    for (const record of mappedData) {
      await this.structuredResponseRepository.create(record as StructuredResponse);
    }

    console.log('Data migration for DCF 305 completed successfully.');
    return {message: 'Data migration for DCF 305 completed successfully.'};
  }
  @post('/migrate-316')
  @response(200, {
    description: 'Data migration process for DCF 285 initiated',
  })
  async migrateData316() {
    const dcfId = 316;
    console.log(`Starting data migration for DCF ID: ${dcfId}`);

    const DPAN0040Options = [
      {name: "Hazardous Waste", id: 1},
      {name: "Non- Hazardous waste", id: 2},
    ];
    const DPAN0042Options = [
      {name: "Food Waste", id: 14},
      {name: "Plastic Waste", id: 2},
      {name: "Metals - all types", id: 3},
      {name: "Inert Waste (e.g. soil, rubble, sand etc.)", id: 15},
      {name: "Asphalt Waste", id: 16},
      {name: "Domestic Waste / Office Waste", id: 17},
      {name: "Concrete and masonry waste", id: 18},
      {name: "Glass - all types", id: 11},
      {name: "Others", id: 12},
    ];
    const DPAN0041Options = [
      {name: 'Spent Chemical Waste (Acid,  Alkali, Solvents etc.) and Packaging', id: 7},
      {name: 'Metal Waste containing hazardous substances', id: 8},
      {name: 'Flammable Material', id: 9},
      {name: 'Welding Waste', id: 10},
      {name: 'Metal Finishing Waste', id: 11},
      {name: 'Waste Paint and Packaging Cans', id: 12},
      {name: 'Cement Waste', id: 13},
      {name: 'Abrasive Blasting Waste', id: 14},
      {name: 'Oil and Petroleum Waste, Lubricants and Grease', id: 15},
      {name: 'Waste electrical cables, electrical waste products', id: 16},
      {name: 'Others', id: 6}
    ];

    const submissions = await this.quantitativeSubmissionsRepository.find({
      where: {dcfId: dcfId, userProfileId: 291},
    });
    await this.structuredResponseRepository.deleteAll({dcfId: dcfId, userProfileId: 291});

    if (!submissions.length) {

      return {message: 'No submissions found, inserted empty response.'};
    }

    const mappedData: Partial<StructuredResponse>[] = [];

    for (const submission of submissions) {
      if (submission.edit === 1 && submission.response && Array.isArray(submission.response) && submission.response.length === 0) {
        await this.structuredResponseRepository.create({
          dcfId,
          title: 'No submission for this reporting period',
          label: 'No submission for this reporting period',
          value: '-',
          formType: 2,
          submitDcfId: submission.id,
          isNull: true,
          created_on: submission.last_modified_on,
          created_by: submission.last_modified_by,
          uom: '-', userProfileId: 291,
          reporting_period: submission.reporting_period
        });
        continue;
      }

      for (const item of submission?.response || []) {
        const isHazardous = item.DPAN0040 === 1;
        const parentOption = DPAN0040Options.find(opt => opt.id === item.DPAN0040);
        const currentOption = isHazardous
          ? DPAN0041Options.find(opt => opt.id === item.DPAN0041)
          : DPAN0042Options.find(opt => opt.id === item.DPAN0042);

        if (!parentOption || !currentOption) continue;

        const result: Partial<StructuredResponse> = {
          dcfId,
          title: `${parentOption?.name} > ${currentOption?.name} > MT`,
          label: isHazardous ? item.DPAN0041 === 6 ? 'Others/' + item.DPAN0041A : currentOption?.name : item.DPAN0042 === 12 ? 'Others/' + item.DPAN0042A : currentOption?.name,
          value: item.DPAN0043,
          currentId: isHazardous ? item.DPAN0041 : item.DPAN0042,
          parentId: item.DPAN0040,
          formType: 2,
          dataType: 2,
          isNull: false,
          attachment: item.attachment || null,
          submitDcfId: submission.id,
          reporting_period: submission.reporting_period,
          uom: 'MT',
          uniqueId: `${item.DPAN0040}-${isHazardous ? item.DPAN0041 : item.DPAN0042}`,
          maskId: item.id,
          userProfileId: 291,
          created_by: submission.last_modified_by,
          created_on: submission.last_modified_on
        };

        mappedData.push(result);
      }
    }

    for (const record of mappedData) {
      await this.structuredResponseRepository.create(record as StructuredResponse);
    }

    console.log('Data migration for DCF 285 completed successfully.');
    return {message: 'Data migration for DCF 285 completed successfully.'};
  }


  @post('/migrate-383')
  @response(200, {
    description: 'Waste data structure migration from old format to new API-driven format',
  })
  async migrateData383() {
    const dcfId = 383;
    console.log(`Starting waste data structure migration for DCF ID: ${dcfId}`);

    // Define the old format options for reference
    const DPAN0040Options = [
      {name: "Hazardous Waste", id: 1},
      {name: "Non- Hazardous waste", id: 2},
    ];

    const DPAN0042Options = [
      {name: "Paper - all types", id: 1},
      {name: "Plastic - all types", id: 2},
      {name: "Metals - all types", id: 3},
      {name: "Electrical items - WEEE - (ex. printers)", id: 4},
      {name: "General Waste - Commecial and industrial waste", id: 6},
      {name: "General Waste - Organic: mixed food and garden waste", id: 7},
      {name: "General Waste - Organic: garden waste", id: 8},
      {name: "General Waste - Organic: food and drink waste", id: 9},
      {name: "General Waste - Household residual waste", id: 10},
      {name: "Glass - all types", id: 11},
      {name: "Construction and demolition waste", id: 19},
      {name: "Others", id: 12},
    ];

    const DPAN0041Options = [
      {name: "Used Oil", id: 17},
      {name: "Wastes/Residues Containing Oil", id: 18},
      {name: "Phosphate Sludge", id: 19},
      {name: "Waste Thinner", id: 20},
      {name: "Electrical items - Batteries", id: 25},
      {name: "Wastes and Residues (Paint Sludge)", id: 21},
      {name: "Acid/Chemical/Paint/Thinner Containers", id: 22},
      {name: "Chemical Sludge from Waste Water Treatment Plants", id: 23},
      {name: "Furnace Oil Sludge", id: 24},
      {name: "ATFD Salt", id: 24},
      {name: "Bio-medical waste", id: 7},
      {name: "Radioactive waste", id: 8},
      {name: "Others - Please include in remarks", id: 6},
    ];

    const DPAN0044Options = [
      {name: "Recovery of waste", id: 1},
      {name: "Disposal of waste", id: 2},
    ];
    const result: any = [], actual: any = [];
    const unknownMethodsFound: any = [];

    // Get API mappings for waste categories and types
    const apiMappings = await this.getWasteApiMappings();

    const submissions = await this.quantitativeSubmissionsRepository.find({
      where: {dcfId: dcfId, userProfileId: {neq: 94}},
    });

    if (!submissions.length) {
      console.log('No submissions found for migration.');
      return {message: 'No submissions found for migration.'};
    }

    let migratedCount = 0;

    for (const submission of submissions) {
      if (!submission.response || !Array.isArray(submission.response)) {
        continue;
      }
      actual.push({id: submission.id, response: submission.response})
      const migratedResponse = await Promise.all(
        submission.response.map(async (responseItem: any) => {
          // Check if this is old format data (has DPAN0040 field)
          if (!responseItem.DPAN0040) {
            return responseItem; // Return unchanged if not old format
          }

          // Transform old format to new format
          const newFormatData = await this.transformWasteRecord(responseItem, apiMappings);

          // Collect unknown methods if any
          if (newFormatData._unknownMethods) {
            unknownMethodsFound.push({
              submissionId: submission.id,
              recordId: responseItem.id,
              unknownMethods: newFormatData._unknownMethods
            });
            // Remove from the final data
            delete newFormatData._unknownMethods;
          }

          migratedCount++;
          return newFormatData;
        })
      );
      result.push(migratedResponse)
      // Update the submission with migrated response

      // await this.quantitativeSubmissionsRepository.updateById(submission.id, {
      //   response: migratedResponse
      // });

      console.log(`Migrated submission ID: ${submission.id} with ${migratedResponse.length} response items`);
    }

    console.log(`Waste data migration for DCF ${dcfId} completed successfully. Migrated ${migratedCount} records across ${submissions.length} submissions.`);

    // Log unknown methods summary
    if (unknownMethodsFound.length > 0) {
      console.log(`\n❗ UNKNOWN METHODS FOUND - NEED REVIEW:`);
      unknownMethodsFound.forEach((item: any) => {
        console.log(`  Submission ${item.submissionId}, Record ${item.recordId}: ${item.unknownMethods.map((m: any) => `${m.mode}(${m.qty})`).join(', ')}`);
      });
    }

    return {
      result,
      actual,
      unknownMethods: unknownMethodsFound,
      message: `Waste data migration for DCF ${dcfId} completed successfully. Migrated ${migratedCount} records across ${submissions.length} submissions.${unknownMethodsFound.length > 0 ? ` Found ${unknownMethodsFound.length} records with unknown methods that need review.` : ''}`
    };
  }

  /**
   * Get API mappings for waste categories and types
   */
  private async getWasteApiMappings(): Promise<any> {
    try {
      // Load waste API data from JSON file
      const fs = require('fs');
      const path = require('path');
      const wasteApiPath = path.join(__dirname, '../../wasteAPI.json');
      const wasteApiData = JSON.parse(fs.readFileSync(wasteApiPath, 'utf8'));

      // Extract the waste structure
      const wasteCategory = wasteApiData[0]?.newEfCategories?.find((cat: any) => cat.title === "Waste_New");
      const categories = wasteCategory?.newEfSubcategory1s || [];

      // Build mapping objects for easy lookup
      const mappings = {
        categories: categories,
        hazardousWasteTypes: this.buildHazardousWasteTypeMapping(categories),
        nonHazardousWasteTypes: this.buildNonHazardousWasteTypeMapping(categories),
        managementMethods: this.buildManagementMethodMapping(categories),
        disposalMethods: this.buildDisposalMethodMapping(categories),
        recoveryMethods: this.buildRecoveryMethodMapping(categories)
      };

      return mappings;
    } catch (error) {
      console.error('Error loading waste API mappings:', error);
      return {
        categories: [],
        hazardousWasteTypes: {},
        nonHazardousWasteTypes: {},
        managementMethods: {},
        disposalMethods: {},
        recoveryMethods: {}
      };
    }
  }

  /**
   * Build hazardous waste type mapping from old IDs to new API IDs
   */
  private buildHazardousWasteTypeMapping(categories: any[]): {[key: number]: number} {
    const hazardousCategory = categories.find(cat => cat.title === "Hazardous Waste");
    if (!hazardousCategory) return {};

    const mapping: {[key: number]: number} = {};

    // Map old hazardous waste type IDs to new API IDs
    const wasteTypes = hazardousCategory.newEfSubcategory2s || [];

    // Based on the old DPAN0041Options and API data
    mapping[17] = wasteTypes.find((wt: any) => wt.title === "Used Oil")?.id || 923;
    mapping[18] = wasteTypes.find((wt: any) => wt.title === "Wastes/Residues Containing Oil")?.id || 924;
    mapping[19] = wasteTypes.find((wt: any) => wt.title === "Phosphate Sludge")?.id || 925;
    mapping[20] = wasteTypes.find((wt: any) => wt.title === "Waste Thinner")?.id || 926;
    mapping[21] = wasteTypes.find((wt: any) => wt.title === "Wastes and Residues (Paint Sludge)")?.id || 927;
    mapping[22] = wasteTypes.find((wt: any) => wt.title === "Acid/Chemical/Paint/Thinner Containers")?.id || 928;
    mapping[23] = wasteTypes.find((wt: any) => wt.title === "Chemical Sludge from Waste Water Treatment Plants")?.id || 929;
    mapping[24] = wasteTypes.find((wt: any) => wt.title === "Furnace Oil Sludge")?.id || 930;
    mapping[25] = wasteTypes.find((wt: any) => wt.title === "Battery Waste")?.id || 935;
    mapping[7] = wasteTypes.find((wt: any) => wt.title === "Bio-medical waste")?.id || 932;
    mapping[8] = wasteTypes.find((wt: any) => wt.title === "Radioactive waste")?.id || 933;
    mapping[6] = wasteTypes.find((wt: any) => wt.title === "Others")?.id || 934;

    return mapping;
  }

  /**
   * Build non-hazardous waste type mapping from old IDs to new API IDs
   */
  private buildNonHazardousWasteTypeMapping(categories: any[]): {[key: number]: number} {
    const nonHazardousCategory = categories.find(cat => cat.title === "Non-Hazardous Waste");
    if (!nonHazardousCategory) return {};

    const mapping: {[key: number]: number} = {};

    // Map old non-hazardous waste type IDs to new API IDs
    const wasteTypes = nonHazardousCategory.newEfSubcategory2s || [];

    // Based on the old DPAN0042Options and API data
    mapping[1] = wasteTypes.find((wt: any) => wt.title === "Paper - all types")?.id || 936;
    mapping[2] = wasteTypes.find((wt: any) => wt.title === "Plastic - all types")?.id || 937;
    mapping[3] = wasteTypes.find((wt: any) => wt.title === "Metals - all types")?.id || 938;
    mapping[4] = wasteTypes.find((wt: any) => wt.title === "Electrical items - WEEE - (ex. printers)")?.id || 939;
    mapping[6] = wasteTypes.find((wt: any) => wt.title === "General Waste - Commercial and industrial waste")?.id || 940;
    mapping[7] = wasteTypes.find((wt: any) => wt.title === "General Waste - Organic: mixed food and garden waste")?.id || 941;
    mapping[8] = wasteTypes.find((wt: any) => wt.title === "General Waste - Organic: garden waste")?.id || 942;
    mapping[9] = wasteTypes.find((wt: any) => wt.title === "General Waste - Organic: food and drink waste")?.id || 943;
    mapping[10] = wasteTypes.find((wt: any) => wt.title === "General Waste - Household residual waste")?.id || 944;
    mapping[11] = wasteTypes.find((wt: any) => wt.title === "Glass - all types")?.id || 945;
    mapping[19] = wasteTypes.find((wt: any) => wt.title === "Construction and demolition waste")?.id || 946;
    mapping[12] = wasteTypes.find((wt: any) => wt.title === "Others")?.id || 947;

    return mapping;
  }

  /**
   * Build management method mapping
   */
  private buildManagementMethodMapping(categories: any[]): {[key: string]: {[key: number]: {[key: number]: number}}} {
    const mapping: {[key: string]: {[key: number]: {[key: number]: number}}} = {
      hazardous: {},
      nonHazardous: {}
    };

    // For hazardous waste
    const hazardousCategory = categories.find(cat => cat.title === "Hazardous Waste");
    if (hazardousCategory) {
      const wasteTypes = hazardousCategory.newEfSubcategory2s || [];
      wasteTypes.forEach((wasteType: any) => {
        const managementMethods = wasteType.newEfSubcategory3s || [];
        const recoveryMethod = managementMethods.find((mm: any) => mm.title === "Recovery of Waste");
        const disposalMethod = managementMethods.find((mm: any) => mm.title === "Disposal of waste");

        mapping.hazardous[wasteType.id] = {};
        if (recoveryMethod) mapping.hazardous[wasteType.id][1] = recoveryMethod.id;
        if (disposalMethod) mapping.hazardous[wasteType.id][2] = disposalMethod.id;
      });
    }

    // For non-hazardous waste
    const nonHazardousCategory = categories.find(cat => cat.title === "Non-Hazardous Waste");
    if (nonHazardousCategory) {
      const wasteTypes = nonHazardousCategory.newEfSubcategory2s || [];
      wasteTypes.forEach((wasteType: any) => {
        const managementMethods = wasteType.newEfSubcategory3s || [];
        const recoveryMethod = managementMethods.find((mm: any) => mm.title === "Recovery of Waste");
        const disposalMethod = managementMethods.find((mm: any) => mm.title === "Disposal of waste");

        mapping.nonHazardous[wasteType.id] = {};
        if (recoveryMethod) mapping.nonHazardous[wasteType.id][1] = recoveryMethod.id;
        if (disposalMethod) mapping.nonHazardous[wasteType.id][2] = disposalMethod.id;
      });
    }

    return mapping;
  }

  /**
   * Build disposal method mapping
   */
  private buildDisposalMethodMapping(categories: any[]): {[key: string]: {[key: string]: number}} {
    const mapping: {[key: string]: {[key: string]: number}} = {};

    // Build mapping for disposal method names to IDs
    categories.forEach(category => {
      const wasteTypes = category.newEfSubcategory2s || [];
      wasteTypes.forEach((wasteType: any) => {
        const managementMethods = wasteType.newEfSubcategory3s || [];
        const disposalMethod = managementMethods.find((mm: any) => mm.title === "Disposal of waste");

        if (disposalMethod) {
          const disposalOptions = disposalMethod.newEfSubcategory4s || [];
          disposalOptions.forEach((option: any) => {
            mapping[option.title] = option.id;
          });
        }
      });
    });

    return mapping;
  }

  /**
   * Build recovery method mapping
   */
  private buildRecoveryMethodMapping(categories: any[]): {[key: string]: {[key: string]: number}} {
    const mapping: {[key: string]: {[key: string]: number}} = {};

    // Build mapping for recovery method names to IDs
    categories.forEach(category => {
      const wasteTypes = category.newEfSubcategory2s || [];
      wasteTypes.forEach((wasteType: any) => {
        const managementMethods = wasteType.newEfSubcategory3s || [];
        const recoveryMethod = managementMethods.find((mm: any) => mm.title === "Recovery of Waste");

        if (recoveryMethod) {
          const recoveryOptions = recoveryMethod.newEfSubcategory4s || [];
          recoveryOptions.forEach((option: any) => {
            mapping[option.title] = option.id;
          });
        }
      });
    });

    return mapping;
  }

  /**
   * Transform individual waste record from old format to new format
   */
  private async transformWasteRecord(oldRecord: any, apiMappings: any): Promise<any> {
    try {
      // Map basic fields from old to new format
      const wasteCategory = this.mapWasteCategory(oldRecord.DPAN0040);
      const wasteType = await this.mapWasteType(oldRecord, apiMappings);
      const wasteTypeOther = this.getOthersDescription(oldRecord);
      const quantity = oldRecord.DPAN0043 || 0;

      // Check for incorrectly submitted disposal methods that should be recovery
      const correctedRecord = this.correctIncorrectSubmissions(oldRecord);

      // Transform recovery methods from old format (using corrected record)
      const recoveryMethods = this.mapRecoveryMethods(correctedRecord, apiMappings);

      // Transform disposal methods from old format (using corrected record)
      const disposalMethods = this.mapDisposalMethods(correctedRecord, apiMappings);

      // Get corrected management method
      const correctedManagementMethod = await this.getManagementMethodId(correctedRecord, apiMappings);

      // Create new format record
      const newFormatRecord = {
        id: oldRecord.id, // Preserve original ID
        wasteCategory: wasteCategory,
        wasteType: wasteType,
        wasteTypeOther: wasteTypeOther,
        quantity: quantity,
        managementMethod: correctedManagementMethod,
        recoveryMethods: recoveryMethods,
        disposalMethods: disposalMethods,
        // Preserve any additional fields that might exist
        attachment: oldRecord.attachment || null
      };

      console.log(`Transformed waste record ID: ${oldRecord.id} from old format to new format`);
      return newFormatRecord;
    } catch (error) {
      console.error(`Error transforming waste record ID: ${oldRecord.id}:`, error);
      return oldRecord; // Return original if transformation fails
    }
  }

  /**
   * Map old category ID to new API category ID
   */
  private mapWasteCategory(dpan0040: number): number {
    const categoryMap: {[key: number]: number} = {
      1: 508, // Hazardous Waste
      2: 509  // Non-Hazardous Waste
    };
    return categoryMap[dpan0040] || 508;
  }

  /**
   * Map waste type from old format to new API IDs
   */
  private async mapWasteType(oldRecord: any, apiMappings: any): Promise<number> {
    const oldWasteTypeId = oldRecord.DPAN0040 === 1 ? oldRecord.DPAN0041 : oldRecord.DPAN0042;

    // Map old waste type IDs to new API IDs using apiMappings
    if (oldRecord.DPAN0040 === 1) {
      // Hazardous waste
      return apiMappings.hazardousWasteTypes[oldWasteTypeId] || oldWasteTypeId;
    } else {
      // Non-hazardous waste
      return apiMappings.nonHazardousWasteTypes[oldWasteTypeId] || oldWasteTypeId;
    }
  }

  /**
   * Get "Others" description from old format
   */
  private getOthersDescription(oldRecord: any): string {
    if (oldRecord.DPAN0040 === 1) {
      // Hazardous waste "Others" description
      return oldRecord.DPAN0041A || "";
    } else {
      // Non-hazardous waste "Others" description
      return oldRecord.DPAN0042A || "";
    }
  }

  /**
   * Get management method ID based on old format data
   */
  private async getManagementMethodId(oldRecord: any, apiMappings: any): Promise<number> {
    const isRecovery = oldRecord.DPAN0044 === 1;
    const wasteTypeId = oldRecord.DPAN0040 === 1 ?
      apiMappings.hazardousWasteTypes[oldRecord.DPAN0041] :
      apiMappings.nonHazardousWasteTypes[oldRecord.DPAN0042];

    const categoryType = oldRecord.DPAN0040 === 1 ? 'hazardous' : 'nonHazardous';
    const managementMethodType = isRecovery ? 1 : 2;

    // Get management method ID from mappings
    return apiMappings.managementMethods[categoryType]?.[wasteTypeId]?.[managementMethodType] ||
      (isRecovery ? 1652 : 1653); // Fallback to default IDs
  }

  /**
   * Map recovery methods from old format to new format
   */
  private mapRecoveryMethods(oldRecord: any, apiMappings: any): {[key: string]: number} {
    const recoveryMethods: {[key: string]: number} = {};

    if (oldRecord.DPAN0044 !== 1) return recoveryMethods; // Not recovery

    // Map individual recovery methods using API mappings
    if (oldRecord.DPA0068A && oldRecord.DPA0068 > 0) {
      const methodId = apiMappings.recoveryMethods["Preparation for reuse"];
      if (methodId) recoveryMethods[methodId] = oldRecord.DPA0068;
    }

    if (oldRecord.DPA0069A && oldRecord.DPA0069 > 0) {
      const methodId = apiMappings.recoveryMethods["Recycling"];
      if (methodId) recoveryMethods[methodId] = oldRecord.DPA0069;
    }

    // Map recovery method arrays
    if (oldRecord.DPA0070A && oldRecord.DPA0070 && Array.isArray(oldRecord.DPA0070)) {
      oldRecord.DPA0070.forEach((method: any) => {
        if (method.mode && method.qty > 0) {
          const methodId = apiMappings.recoveryMethods[method.mode];
          if (methodId) {
            recoveryMethods[methodId] = method.qty;
          }
        }
      });
    }

    return recoveryMethods;
  }

  /**
   * Map disposal methods from old format to new format
   */
  private mapDisposalMethods(oldRecord: any, apiMappings: any): {[key: string]: number} {
    const disposalMethods: {[key: string]: number} = {};

    if (oldRecord.DPAN0044 !== 2) return disposalMethods; // Not disposal

    // Map individual disposal methods using API mappings
    if (oldRecord.DPA0087A && oldRecord.DPA0087 > 0) {
      const methodId = apiMappings.disposalMethods["Incineration (with energy recovery)"];
      if (methodId) disposalMethods[methodId] = oldRecord.DPA0087;
    }

    if (oldRecord.DPA0088A && oldRecord.DPA0088 > 0) {
      const methodId = apiMappings.disposalMethods["Incineration (without energy recovery)"];
      if (methodId) disposalMethods[methodId] = oldRecord.DPA0088;
    }

    if (oldRecord.DPA0089A && oldRecord.DPA0089 > 0) {
      const methodId = apiMappings.disposalMethods["Landfilling"];
      if (methodId) disposalMethods[methodId] = oldRecord.DPA0089;
    }

    if (oldRecord.DPA0091A && oldRecord.DPA0091 > 0) {
      const methodId = apiMappings.disposalMethods["Co-Processing"];
      if (methodId) disposalMethods[methodId] = oldRecord.DPA0091;
    }

    // Map disposal method arrays
    if (oldRecord.DPA0090A && oldRecord.DPA0090 && Array.isArray(oldRecord.DPA0090)) {
      oldRecord.DPA0090.forEach((method: any) => {
        if (method.mode && method.qty > 0) {
          // Specifically handle Co-processing and Vermicompost (case-insensitive)
          if (method.mode.toLowerCase() === "co-processing") {
            const methodId = apiMappings.disposalMethods["Co-Processing"];
            if (methodId) {
              disposalMethods[methodId] = method.qty;
            }
          } else if (method.mode.toLowerCase() === "vermicompost") {
            const methodId = apiMappings.disposalMethods["Vermicompost"] || apiMappings.disposalMethods["Other disposal operations"];
            if (methodId) {
              disposalMethods[methodId] = method.qty;
            }
          } else {
            // Handle other disposal methods
            const methodId = apiMappings.disposalMethods[method.mode];
            if (methodId) {
              disposalMethods[methodId] = method.qty;
            }
          }
        }
      });
    }

    return disposalMethods;
  }

  /**
   * Correct incorrectly submitted disposal methods that should be recovery
   * When DPA0090A is true and array contains methods other than "Co-processing",
   * those should be treated as Recovery methods
   */
  private correctIncorrectSubmissions(oldRecord: any): any {
    const correctedRecord = {...oldRecord};

    // Check if DPA0090A is true and has array data
    if (oldRecord.DPA0090A && oldRecord.DPA0090 && Array.isArray(oldRecord.DPA0090)) {
      // Log all methods in DPA0090 array for analysis
      const allMethods = oldRecord.DPA0090.filter((method: any) => method.mode && method.mode.trim() !== "");
      console.log(`Submission ID ${oldRecord.id} - DPA0090 methods found:`, allMethods.map((m: any) => `${m.mode}: ${m.qty}`));

      const hasNonCoProcessingMethods = oldRecord.DPA0090.some((method: any) =>
        method.mode && method.mode.trim() !== "" && method.mode !== "Co-processing" && method.mode !== "Vermicompost" && method.qty > 0
      );

      if (hasNonCoProcessingMethods) {
        console.log(`Correcting submission ID ${oldRecord.id}: Converting disposal methods to recovery methods`);

        // Change management method from Disposal (2) to Recovery (1)
        correctedRecord.DPAN0044 = 1;

        // Move non-Co-processing methods to recovery array (DPA0070)
        const recoveryMethods: any[] = [];
        const remainingDisposalMethods: any[] = [];
        const ignoredMethods: any[] = [];

        // Define disposal methods that should stay in disposal
        const disposalMethods = ["Co-processing", "Vermicompost"];

        // Define recovery methods that should be moved to recovery (all recycling-related)
        const recoveryMethodNames = [
          "Recycler", "SSWML", "Scrap dealer", "Supplier/recycler", "Recycling",
          "Empty Containers", "supplier/recycler sswml", "through recycler",
          "vermicompost", "Recycling/OEM", "Disposal through recycling",
          "Recycler/OEM", "Buyback/sent back to supplier"
        ];

        oldRecord.DPA0090.forEach((method: any) => {
          if (method.mode && method.mode.trim() !== "") {
            if (method.qty > 0) {
              // Check if it's a disposal method (case-insensitive)
              const isDisposalMethod = disposalMethods.some(dm =>
                dm.toLowerCase() === method.mode.toLowerCase()
              );

              // Check if it's a known recovery method (case-insensitive)
              const isRecoveryMethod = recoveryMethodNames.some(rm =>
                rm.toLowerCase() === method.mode.toLowerCase()
              );

              if (isDisposalMethod) {
                // Keep in disposal
                remainingDisposalMethods.push(method);
                console.log(`  ✓ Keeping in Disposal: ${method.mode} (${method.qty})`);
              } else if (isRecoveryMethod) {
                // Move to recovery as "Recycling"
                recoveryMethods.push({...method, mode: "Recycling"});
                console.log(`  → Moving to Recovery as Recycling: ${method.mode} (${method.qty})`);
              } else {
                // Unknown method - flag for review
                ignoredMethods.push(method);
                console.log(`  ❓ UNKNOWN METHOD - Please review: ${method.mode} (${method.qty})`);
              }
            } else {
              ignoredMethods.push(method);
              console.log(`  ⚠ Ignored (qty=0): ${method.mode} (${method.qty})`);
            }
          } else {
            ignoredMethods.push(method);
            console.log(`  ⚠ Ignored (empty mode): ${JSON.stringify(method)}`);
          }
        });

        // Log summary
        console.log(`  Summary for ID ${oldRecord.id}:`);
        console.log(`    - Moved to Recovery: ${recoveryMethods.length} methods`);
        console.log(`    - Kept in Disposal: ${remainingDisposalMethods.length} methods`);
        console.log(`    - Ignored: ${ignoredMethods.length} methods`);

        // Check for unknown methods that need review
        const unknownMethods = ignoredMethods.filter(m =>
          m.mode && m.mode.trim() !== "" && m.qty > 0 &&
          !disposalMethods.some(dm => dm.toLowerCase() === m.mode.toLowerCase()) &&
          !recoveryMethodNames.some(rm => rm.toLowerCase() === m.mode.toLowerCase())
        );

        if (unknownMethods.length > 0) {
          console.log(`    ❗ UNKNOWN METHODS NEED REVIEW: ${unknownMethods.map(m => `${m.mode}(${m.qty})`).join(', ')}`);

          // Store unknown methods for return response
          correctedRecord._unknownMethods = unknownMethods;
        }

        // Update the arrays
        correctedRecord.DPA0070 = recoveryMethods;
        correctedRecord.DPA0070A = recoveryMethods.length > 0;

        // Update disposal array (keep only Co-processing/Vermicompost if any)
        correctedRecord.DPA0090 = remainingDisposalMethods;
        correctedRecord.DPA0090A = remainingDisposalMethods.length > 0;

        // If no disposal methods left, change back to recovery completely
        if (remainingDisposalMethods.length === 0) {
          correctedRecord.DPAN0044 = 1; // Recovery of waste
          console.log(`  → Changed to Recovery only (no disposal methods left)`);
        }
      } else {
        console.log(`Submission ID ${oldRecord.id}: No methods need correction (only Co-processing/Vermicompost/empty found)`);
      }
    }

    return correctedRecord;
  }



  @post('/migrate-11')
  @response(200, {
    description: 'Data migration process for DCF 285 initiated',
  })
  async migrateData11() {
    const dcfId = 11;
    console.log(`Starting data migration for DCF ID: ${dcfId}`);


    const submissions = await this.quantitativeSubmissionsRepository.find({
      where: {dcfId: dcfId, userProfileId: {neq: 94}},
    });
    await this.structuredResponseRepository.deleteAll({dcfId: dcfId, userProfileId: {neq: 94}});
    if (!submissions.length) {

      return {message: 'No submissions found, inserted empty response.'};
    }

    const mappedData: Partial<StructuredResponse>[] = [];


    for (const submission of submissions) {
      if (submission.edit === 1 && submission.response && Array.isArray(submission.response) && submission.response.length === 0) {
        await this.structuredResponseRepository.create({
          dcfId,
          title: 'No submission for this reporting period',
          label: 'No submission for this reporting period',
          value: '-',
          formType: 2,
          submitDcfId: submission.id,
          isNull: true,
          created_on: submission.last_modified_on,
          created_by: submission.last_modified_by,
          uom: '-', userProfileId: submission.userProfileId,
          reporting_period: submission.reporting_period
        });

        console.log(`No response found for submission ID: ${submission.id}, inserted empty response.`);
        continue;
      }

      for (const responseItem of submission?.response || []) {
        const DPA0130 = responseItem.DPA0130;
        const DPA0131 = responseItem.DPA0131;
        const DPA0132 = responseItem.DPA0132;

        // Fetch Titles using Repository `findOne`
        const titleDPA0130 = await this.getTitleFromRepository(this.newEfSubcategory1Repository, DPA0130);
        const titleDPA0131 = await this.getTitleFromRepository(this.newEfSubcategory2Repository, DPA0131);
        const titleDPA0132 = await this.getTitleFromRepository(this.newEfSubcategory3Repository, DPA0132);

        const result: Partial<StructuredResponse> = {
          title: `${titleDPA0130}>${titleDPA0131}>${titleDPA0132}`,
          dcfId,
          formType: 2,
          dataType: 1,
          label: titleDPA0131,
          maskId: responseItem.id,
          submitDcfId: submission.id,
          userProfileId: submission.userProfileId,
          created_by: submission.last_modified_by,
          created_on: submission.last_modified_on,
          currentId: DPA0132,
          uom: titleDPA0132,
          uniqueId: `${DPA0130}-${DPA0131}-${DPA0132}`,
          parentId: DPA0131,
          subCategory1: DPA0130,
          subCategory2: DPA0131,
          subCategory3: DPA0132,
          value: parseFloat(responseItem?.DPA0336 || null),
          reporting_period: submission.reporting_period || [],
          attachment: responseItem.attachment || null
        };



        mappedData.push(result);
      }
    }

    for (const record of mappedData) {
      await this.structuredResponseRepository.create(record as StructuredResponse);
    }

    console.log('Data migration for DCF 285 completed successfully.');
    return {message: 'Data migration for DCF 285 completed successfully.'};
  }
  @post('/migrate-246')
  @response(200, {
    description: 'Data migration process for DCF 245 initiated',
  })
  async migrateData246() {
    const dcfId = 246;
    console.log(`Starting data migration for DCF ID: ${dcfId}`);

    const DPAN0048Options = [
      {name: "Surface water", id: 1},
      {name: "Ground Water", id: 2},
      {name: "Sea Water", id: 3},
      {name: "Produced Water", id: 4},
      {name: "Third-Party Water", id: 5},
      {name: "Others", id: 6}
    ];
    const DPAN0050Options = [
      {name: "(≤1,000 mg/L Total Dissolved Solids)", id: 1},
      {name: "(>1,000 mg/L Total Dissolved Solids)", id: 2},
    ];
    const unitOptions = [
      {name: 'L', id: 1}, {name: 'KL', id: 2}, {name: 'ML', id: 3}
    ]

    const submissions = await this.quantitativeSubmissionsRepository.find({
      where: {dcfId: dcfId, userProfileId: {neq: 94}},
    });
    await this.structuredResponseRepository.deleteAll({dcfId: dcfId, userProfileId: {neq: 94}});

    if (!submissions.length) {

      return {message: 'No submissions found, inserted empty response.'};
    }

    const mappedData: Partial<StructuredResponse>[] = [];

    for (const submission of submissions) {
      if (submission.edit === 1 && submission.response && Array.isArray(submission.response) && submission.response.length === 0) {
        await this.structuredResponseRepository.create({
          dcfId,
          title: 'No submission for this reporting period',
          label: 'No submission for this reporting period',
          value: '-',
          formType: 2,
          submitDcfId: submission.id,
          isNull: true,
          created_on: submission.last_modified_on,
          created_by: submission.last_modified_by,
          uom: '-', userProfileId: submission.userProfileId,
          reporting_period: submission.reporting_period
        });
        continue;
      }

      for (const item of submission?.response || []) {
        const title1 = item.DPAN0048 === 6 ? DPAN0048Options.find((it) => it.id === item.DPAN0048)?.name + '/' + item.DPAN0048A : DPAN0048Options.find((it) => it.id === item.DPAN0048)?.name
        const title2 = unitOptions.find((it: any) => it.id === item.DPAN0049A)?.name || ''

        if (!title1 || !title2) continue;

        const result: Partial<StructuredResponse> = {
          dcfId,
          title: `${title1} > ${title2}`,
          label: title1,
          value: parseFloat(item.DPAN0049),
          currentId: item.DPAN0049A,
          parentId: item.DPAN0048,
          formType: 2,
          dataType: 2,
          isNull: false,
          attachment: item.attachment || null,
          uom: title2,
          uniqueId: `${item.DPAN0048}-${item.DPAN0049A}`,
          maskId: item.id,
          userProfileId: submission.userProfileId,
          additionalValue1: DPAN0050Options.find((i) => i.id === item.DPAN0050)?.name || '',
          additionalValue2: item.DPAN0051,
          conversionUnit: "ML",
          conversionValue: this.getConversionValue(item.DPAN0049, item.DPAN0049A),
          reporting_period: submission.reporting_period,
          submitDcfId: submission.id,
          created_by: submission.last_modified_by,
          created_on: submission.last_modified_on
        };

        mappedData.push(result);
      }
    }

    for (const record of mappedData) {
      await this.structuredResponseRepository.create(record as StructuredResponse);
    }

    console.log('Data migration for DCF 285 completed successfully.');
    return {message: 'Data migration for DCF 285 completed successfully.'};
  }
  @post('/migrate-247')
  @response(200, {
    description: 'Data migration process for DCF 245 initiated',
  })
  async migrateData247() {
    const dcfId = 247;
    console.log(`Starting data migration for DCF ID: ${dcfId}`);

    const unitOptions = [
      {name: 'L', id: 1}, {name: 'KL', id: 2}, {name: 'ML', id: 3}
    ]
    const DPANN0030Options = [
      {name: 'No Treatment', id: 1}, {name: 'With Treatment', id: 2}
    ]
    const DPAN0052Options = [
      {name: "Surface water", id: 1},
      {name: "Ground Water", id: 2},
      {name: "Sea Water", id: 3},
      {name: "Produced Water", id: 4},
      {name: "Third-Party Water", id: 5},
      {name: "Others", id: 6}
    ];
    const DPAN0054Options = [
      {name: "(≤1,000 mg/L Total Dissolved Solids)", id: 1},
      {name: "(>1,000 mg/L Total Dissolved Solids)", id: 2},
      {name: "Not Available", id: 3},
    ];

    const submissions = await this.quantitativeSubmissionsRepository.find({
      where: {dcfId: dcfId, userProfileId: {neq: 94}},
    });
    await this.structuredResponseRepository.deleteAll({dcfId: dcfId, userProfileId: {neq: 94}});

    if (!submissions.length) {

      return {message: 'No submissions found, inserted empty response.'};
    }

    const mappedData: Partial<StructuredResponse>[] = [];

    for (const submission of submissions) {
      if (submission.edit === 1 && submission.response && Array.isArray(submission.response) && submission.response.length === 0) {
        await this.structuredResponseRepository.create({
          dcfId,
          title: 'No submission for this reporting period',
          label: 'No submission for this reporting period',
          value: '-',
          formType: 2,
          submitDcfId: submission.id,
          isNull: true,
          created_on: submission.last_modified_on,
          created_by: submission.last_modified_by,
          uom: '-', userProfileId: submission.userProfileId,
          reporting_period: submission.reporting_period
        });
        continue;
      }

      for (const item of submission?.response || []) {
        const title1 = item.DPAN0052 === 6 ? DPAN0052Options.find((it) => it.id === item.DPAN0052)?.name + '/' + item.DPAN0052A : DPAN0052Options.find((it) => it.id === item.DPAN0052)?.name
        const title2 = DPANN0030Options.find((it) => it.id === item.DPANN0030)?.name || ''
        const title3 = unitOptions.find((it) => it.id === item.DPAN0053A)?.name

        if (!title1 || !title2) continue;

        const result: Partial<StructuredResponse> = {
          dcfId,
          title: `${title1} > ${title2}`,
          label: title1,
          value: parseFloat(item.DPAN0053),
          currentId: item.DPAN0053A,
          parentId: item.DPANN0030,
          formType: 2,
          dataType: 2,
          isNull: false,
          attachment: item.attachment || null,
          uom: title3,
          uniqueId: `${item.DPAN0052}-${item.DPANN0030}-${item.DPAN0053A}`,
          maskId: item.id,
          userProfileId: submission.userProfileId,
          additionalValue1: DPAN0054Options.find((i) => i.id === item.DPAN0054)?.name || '',
          additionalValue2: item.DPAN0055,
          reporting_period: submission.reporting_period,
          submitDcfId: submission.id,
          conversionUnit: "ML",
          conversionValue: this.getConversionValue(item.DPAN0053, item.DPAN0053A),
          created_by: submission.last_modified_by,
          created_on: submission.last_modified_on
        };

        mappedData.push(result);
      }
    }

    for (const record of mappedData) {
      await this.structuredResponseRepository.create(record as StructuredResponse);
    }

    console.log('Data migration for DCF 285 completed successfully.');
    return {message: 'Data migration for DCF 285 completed successfully.'};
  }

  @post('/migrate-297')
  @response(200, {
    description: 'Data migration process for DCF 297 initiated',
  })
  async migrateData297() {
    const dcfId = 297;
    console.log(`Starting data migration for DCF ID: ${dcfId}`);

    // Define options based on the extractData function
    const DPAH0013Options = [
      {name: "Hazardous Waste", id: 1},
      {name: "Non-Hazardous Waste", id: 2},
      {name: "Food Waste", id: 3},
      {name: "Plastic Waste", id: 4},
      {name: "Metal Waste", id: 5},
      {name: "Self Generated", id: 6},
      {name: "Others", id: 99}
    ];

    const DPAH0014Options = [
      {name: "kg", id: 1},
      {name: "tonnes", id: 2},
      {name: "grams", id: 3}
    ];

    const DPAH0017Options = [
      {name: "Recycling", id: 1},
      {name: "Recovery", id: 2},
      {name: "Composting", id: 3},
      {name: "Incineration with energy recovery", id: 4},
      {name: "Incineration without energy recovery", id: 5},
      {name: "Landfilling", id: 6},
      {name: "Other disposal operations", id: 7},
      {name: "Others", id: 8}
    ];

    // Helper function to find option name from options array
    const findFromOptions = (id: number, options: any[]) => {
      const option = options.find(opt => opt.id === id);
      return option ? option.name : 'Unknown';
    };

    const submissions = await this.quantitativeSubmissionsRepository.find({
      where: {
        dcfId: dcfId, id: {
          inq: [
            5218, 5218, 8551, 8551, 9097,
            9182, 5218, 8551, 8551, 9222, 9222,
            9222, 9222, 9224, 9224, 9289, 9185,
            9291, 9291, 9291, 9291, 9291, 9291,
            9289, 9289, 9289, 9310, 9310, 9311,
            9312, 9492, 9492, 9492, 9492, 9290,
            9971, 9971, 9971, 9971, 9971, 9971,
            9971, 9971, 9971, 9971
          ]
        }, userProfileId: 94
      },
    });

    console.log(`Found ${submissions.length} submissions for userProfileId 94, dcfId ${dcfId}`);

    if (!submissions.length) {
      console.log('No submissions found, inserted empty response.');
      return {message: 'No submissions found, inserted empty response.'};
    }

    await this.structuredResponseRepository.deleteAll({dcfId: dcfId, userProfileId: 94});

    const mappedData: Partial<StructuredResponse>[] = [];

    // Extract data function implementation
    const extractData = (data: any[]) => {
      return data
        .map((item: any) => {
          const isSelfGenerated = item.DPAN0048 === 6;
          const wasteCategory = DPAH0013Options.find((opt) => opt.id === item.DPAH0013);
          const unitOption = DPAH0014Options.find((opt) => opt.id === item.DPAH0014);
          const methodOption = DPAH0017Options.find((opt) => opt.id === item.DPAH0017);

          if (!wasteCategory || !unitOption || !methodOption) return null;
          let conversionValue = 0;
          let amount = item.DPAH0015
          let density = item.DPAH0016

          if (item.DPAH0014 === 1) { // kg
            conversionValue = amount / 1000;
          } else if (item.DPAH0014 === 2) { // tonnes
            conversionValue = amount * density * 0.000001;
          } else if (item.DPAH0014 === 3) { // grams
            conversionValue = (amount * density) / 1000;
          }
          return {
            title: wasteCategory.name,
            label: wasteCategory.name,
            value: item.DPAH0015,
            currentId: item.DPAH0014,
            parentId: item.DPAH0013,
            formType: 2,
            dataType: 2,
            isNull: false,
            conversionValue: conversionValue,
            uom: findFromOptions(item.DPAH0014, DPAH0014Options),
            uniqueId: `${item.DPAH0013}-${item.DPAH0014}-${item.DPAH0017}`,
            maskId: item.id,
            additionalValue1: item.DPAH0017 === 8 ? item.DPAH0017B : findFromOptions(item.DPAH0017, DPAH0017Options),
            additionalValue2: item.DPAH0013 === 6 ? item.DPAH0013B : 'NA'
          };
        })
        .filter(Boolean);
    };

    let totalProcessedItems = 0;
    let submissionIndex = 0;

    for (const submission of submissions) {
      submissionIndex++;
      console.log(`Processing submission ${submissionIndex}/${submissions.length}, ID: ${submission.id}, reporting_period: ${JSON.stringify(submission.reporting_period)}`);

      if (submission.edit === 1 && submission.response && Array.isArray(submission.response) && submission.response.length === 0) {
        console.log(`  - Empty submission (edit=1), creating null record`);
        await this.structuredResponseRepository.create({
          dcfId,
          title: 'No submission for this reporting period',
          label: 'No submission for this reporting period',
          value: '-',
          formType: 2,
          submitDcfId: submission.id,
          isNull: true,
          created_on: submission.last_modified_on,
          created_by: submission.last_modified_by,
          uom: '-',
          userProfileId: submission.userProfileId,
          reporting_period: submission.reporting_period
        });
        totalProcessedItems++;
        continue;
      }

      const response = submission.response || [];
      console.log(`  - Response array length: ${response.length}`);

      const extractedData = extractData(response);
      console.log(`  - Extracted data length: ${extractedData.length}`);

      for (const item of extractedData) {
        if (!item) continue; // Skip null items

        // Calculate conversion value based on unit and density


        const result: Partial<StructuredResponse> = {
          dcfId,
          formType: 2,
          dataType: 2,
          label: item.label,
          maskId: item.maskId,
          submitDcfId: submission.id,
          userProfileId: submission.userProfileId,
          value: item.value,
          reporting_period: submission.reporting_period || [],
          title: item.title,
          parentId: item.parentId,
          uniqueId: item.uniqueId,
          uom: item.uom,
          additionalValue1: item.additionalValue1,
          additionalValue2: item.additionalValue2,
          conversionValue: item.conversionValue,
          conversionUnit: "tonnes",
          created_by: submission.last_modified_by,
          created_on: submission.last_modified_on
        };

        mappedData.push(result);
        totalProcessedItems++;
      }
    }

    console.log(`Total items to be created: ${mappedData.length}`);
    console.log(`Total processed items (including null records): ${totalProcessedItems}`);

    for (const record of mappedData) {
      await this.structuredResponseRepository.create(record as StructuredResponse);
    }

    console.log(`Data migration for DCF 297 completed successfully. Created ${totalProcessedItems} total records.`);
    return {message: `Data migration for DCF 297 completed successfully. Created ${totalProcessedItems} total records.`};
  }


  getConversionValue(value: any, unitId: any) {
    if (value === null || value === undefined ||
      unitId === null || unitId === undefined) {
      return null;
    }
    const numValue = parseFloat(value);
    if (isNaN(numValue) || !isFinite(numValue)) {
      return null;
    }
    // Convert to Megalitres (ML) based on unit ID
    if (unitId === 1) { // Liters (L) to ML
      return numValue / 1000000;
    } else if (unitId === 2) { // Kiloliters (KL) to ML
      return numValue / 1000;
    } else if (unitId === 3) { // Already in ML
      return numValue;
    }
    return null;
  };
}


