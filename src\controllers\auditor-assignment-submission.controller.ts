import {inject} from '@loopback/core';
import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  property,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  param,
  patch,
  post,
  put,
  requestBody,
  response
} from '@loopback/rest';
import {DateTime} from 'luxon';
import {AuditorAssignmentSubmission, SupplierAssessmentAssignment} from '../models';
import {AuditorAssignmentSubmissionRepository, SupplierAssessmentAssignmentRepository, UserRoleAuthorizationRepository} from '../repositories';
import {SqsService} from '../services/sqs-service.service';
import {UserProfileController} from './user-profile.controller';
const CryptoJS = require("crypto-js");
export class NewAuditorAssignmentSubmission extends AuditorAssignmentSubmission {
  @property({
    type: 'string',

  })
  sectionId: string;

}

export class AuditorAssignmentSubmissionController {
  constructor(
    @repository(AuditorAssignmentSubmissionRepository)
    public auditorAssignmentSubmissionRepository: AuditorAssignmentSubmissionRepository,
    @repository(SupplierAssessmentAssignmentRepository)
    public supplierAssessmentAssignmentRepository: SupplierAssessmentAssignmentRepository,
    @repository(UserRoleAuthorizationRepository)
    public userRoleAuthorizationRepository: UserRoleAuthorizationRepository,
    @inject('services.SqsService')
    public sqsService: SqsService,
    @inject('controllers.UserProfileController') public userProfileController: UserProfileController,

  ) { }


  @post('/auditor-assignment-submissions')
  @response(200, {
    description: 'AuditorAssignmentSubmission model instance',
    content: {'application/json': {schema: getModelSchemaRef(AuditorAssignmentSubmission)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AuditorAssignmentSubmission, {
            title: 'NewAuditorAssignmentSubmission',
            exclude: ['id'],
          }),
        },
      },
    })
    auditorAssignmentSubmission: Omit<AuditorAssignmentSubmission, 'id'>,
  ): Promise<AuditorAssignmentSubmission> {
    return this.auditorAssignmentSubmissionRepository.create(auditorAssignmentSubmission);
  }
  @post('/section-id/{id}/auditor-assignment-submissions-new/')
  @response(200, {
    description: 'AuditorAssignmentSubmission model instance',
    content: {'application/json': {schema: getModelSchemaRef(AuditorAssignmentSubmission)}},
  })
  async createCustomNew(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AuditorAssignmentSubmission, {
            title: 'NewAuditorAssignmentSubmission',
            exclude: ['id'],
          }),
        },
      },
    })
    auditorAssignmentSubmission: Omit<AuditorAssignmentSubmission, 'id'>,

  ): Promise<any> {
    const {supplierAssessmentAssignmentId, response} = auditorAssignmentSubmission
    const oldData = await this.auditorAssignmentSubmissionRepository.findOne({where: {supplierAssessmentAssignmentId}})
    const assignment = await this.supplierAssessmentAssignmentRepository.findById(supplierAssessmentAssignmentId)
    const newResponse = JSON.parse(auditorAssignmentSubmission?.response || '[]')
    if (assignment) {
      if (newResponse.length) {
        if (oldData && oldData.id) {

          const newObj = {...auditorAssignmentSubmission} as Partial<NewAuditorAssignmentSubmission>;
          await this.auditorAssignmentSubmissionRepository.updateById(oldData.id, {...newObj, response})
          const newData = await this.auditorAssignmentSubmissionRepository.findById(oldData.id)
          return {result: 2, ...newData, supplierResponse: JSON.stringify(assignment?.supplierAssignmentSubmission?.response || '[]')}

        } else if (!oldData) {


          const newData = await this.auditorAssignmentSubmissionRepository.create(auditorAssignmentSubmission);
          return {result: 2, ...newData, supplierResponse: JSON.stringify(assignment?.supplierAssignmentSubmission?.response || '[]')}


        } else {
          return {result: 0, message: 'Something went wrong'}


        }
      } else {
        return {result: 0, message: 'Response should not be empty'}

      }
    } else {
      return {result: 0, message: 'Assignment not found'}

    }


  }
  @post('/section-id/{id}/auditor-assignment-submissions/')
  @response(200, {
    description: 'AuditorAssignmentSubmission model instance',
    content: {'application/json': {schema: getModelSchemaRef(AuditorAssignmentSubmission)}},
  })
  async createCustom(
    @param.path.string('id') id: typeof SupplierAssessmentAssignment.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AuditorAssignmentSubmission, {
            title: 'NewAuditorAssignmentSubmission',
            exclude: ['id'],
          }),
        },
      },
    })
    auditorAssignmentSubmission: Omit<AuditorAssignmentSubmission, 'id'>,

  ): Promise<any> {
    const {supplierAssessmentAssignmentId, response} = auditorAssignmentSubmission
    const oldData = await this.auditorAssignmentSubmissionRepository.findOne({where: {supplierAssessmentAssignmentId}})
    const assignment = await this.supplierAssessmentAssignmentRepository.findById(supplierAssessmentAssignmentId)
    const newResponse = JSON.parse(response || '[]')
    if (assignment) {
      if (oldData && oldData.id) {
        const found = await this.auditorAssignmentSubmissionRepository.findById(oldData.id)
        if (found) {
          const oldResponse = JSON.parse(found?.response || '[]')
          console.log(found?.id, '1')
          if (oldResponse.length) {
            const index = oldResponse.findIndex((item: any) => item.id === Number(id))
            const index2 = newResponse.findIndex((item: any) => item.id === Number(id))
            console.log(index, oldResponse.map((x: any) => x.id), id, 'index1')
            console.log(index2, newResponse.map((x: any) => x.id), id, 'index2')

            if (index !== -1 && index2 !== -1) {
              oldResponse[index] = newResponse[index2]
              const newObj = {...auditorAssignmentSubmission} as Partial<NewAuditorAssignmentSubmission>;




              await this.auditorAssignmentSubmissionRepository.updateById(oldData.id, {...newObj, response: JSON.stringify(oldResponse)})
              const newData = await this.auditorAssignmentSubmissionRepository.findById(oldData.id)
              return {result: 2, ...newData, supplierResponse: JSON.stringify(assignment?.supplierAssignmentSubmission?.response || '[]')}
            } else {

              return {result: 0, message: 'Invalid Section Id'}
            }
          } else {

            return {result: 0, message: 'No Past Data Found'}

          }


        } else {
          return {result: 0, message: 'No Past Data Found'}
        }
      } else if (!oldData) {


        const newData = await this.auditorAssignmentSubmissionRepository.create(auditorAssignmentSubmission);
        return {result: 2, ...newData, supplierResponse: JSON.stringify(assignment?.supplierAssignmentSubmission?.response || '[]')}


      } else {
        return {result: 0, message: 'Something went wrong'}


      }
    } else {
      return {result: 0, message: 'Assignment not found'}

    }


  }
  @post('/fetch-auditor-assignment-submissions-custom')
  @response(200, {
    description: 'AuditorAssignmentSubmission model instance',
    content: {'application/json': {schema: getModelSchemaRef(AuditorAssignmentSubmission)}},
  })
  async fetchCustom(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            properties: {
              sectionId: {type: 'number'},
              supplierAssessmentAssignmentId: {type: 'string'}
            }
          },
        },
      },
    })
    auditorAssignmentSubmission: {sectionId: number, supplierAssessmentAssignmentId: string},
  ): Promise<any> {
    const {supplierAssessmentAssignmentId, sectionId} = auditorAssignmentSubmission
    const oldData = await this.auditorAssignmentSubmissionRepository.findOne({where: {supplierAssessmentAssignmentId}})
    const assignment = await this.supplierAssessmentAssignmentRepository.findById(supplierAssessmentAssignmentId)

    if (assignment) {
      if (oldData && oldData.id) {

        return {result: 2, ...oldData, supplierResponse: JSON.stringify(assignment?.supplierAssignmentSubmission?.response || '[]')}

      } else if (!oldData) {
        return {result: 0, message: 'Invalid JSON format in response field.'};


      } else {
        return {result: 0, message: 'Something went wrong'}


      }
    }


  }
  @get('/auditor-assignment-submissions/count')
  @response(200, {
    description: 'AuditorAssignmentSubmission model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(AuditorAssignmentSubmission) where?: Where<AuditorAssignmentSubmission>,
  ): Promise<Count> {
    return this.auditorAssignmentSubmissionRepository.count(where);
  }

  @get('/auditor-assignment-submissions')
  @response(200, {
    description: 'Array of AuditorAssignmentSubmission model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(AuditorAssignmentSubmission, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(AuditorAssignmentSubmission) filter?: Filter<AuditorAssignmentSubmission>,
  ): Promise<AuditorAssignmentSubmission[]> {
    return this.auditorAssignmentSubmissionRepository.find(filter);
  }

  @get('/auditor-assignment-submissions-response')
  @response(200, {
    description: 'Array of AuditorAssignmentSubmission model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(AuditorAssignmentSubmission, {includeRelations: true}),
        },
      },
    },
  })
  async findResponse(
    @param.filter(AuditorAssignmentSubmission) filter?: Filter<AuditorAssignmentSubmission>,
  ): Promise<any> {
    const data = await this.auditorAssignmentSubmissionRepository.find({
      include: [{relation: 'vendor'}],
    });

    // Function to determine MSI Rating
    const getMsiRating = (score: number): string => {
      if (score > 86) return "Platinum";
      if (score >= 71 && score <= 85) return "Gold";
      if (score >= 56 && score <= 70) return "Silver";
      if (score >= 41 && score <= 55) return "Bronze";
      return "Needs Improvement";
    };

    const categoryList = {
      1: 'Forging & Machining',
      2: 'Casting & Machining',
      3: 'Pressing & Fabrication',
      4: 'Proprietary Mechanical',
      5: 'Proprietary Electrical',
      6: 'Plastics, Rubber, Painting and Stickers',
      7: 'EV/3W/2W',
      8: 'BW',
      9: 'Accessories'
    };
    // Process the records asynchronously
    const responseData: any[] = await Promise.all(
      data.map(async (record) => {
        const recordWithAssignment: any = {...record};

        // Fetch supplierAssessmentAssignment if exists
        if (record.supplierAssessmentAssignmentId) {
          recordWithAssignment.supplierAssessmentAssignment =
            await this.supplierAssessmentAssignmentRepository.findById(record.supplierAssessmentAssignmentId);
        }

        interface AssessmentSubSection {
          title: string;
          totalScore: number;
          totalCompleted: boolean;
        }

        interface AssessmentSection {
          title: string;
          sectionTotalScore: number;
          completed: boolean;
          assessmentSubSection1s: AssessmentSubSection[];
        }

        let parsedResponse: AssessmentSection[] = [];

        try {
          parsedResponse = JSON.parse(record.response || '[]') as AssessmentSection[];
        } catch (error) {
          console.error('Error parsing JSON response:', error);
        }

        // Extract relevant section data
        const extractSectionData = (title: string) => {
          const section = parsedResponse.find((sec) => sec.title === title);
          if (section) {
            return {
              title: section.title,
              totalScore: section.sectionTotalScore,
              totalCompleted: section.completed,
            };
          }
          return null;
        };

        const extractEnvironmentData = (title: string) => {
          const section = parsedResponse.find((sec) => sec.title === title);
          if (section) {
            return section.assessmentSubSection1s.reduce((acc, subsection) => {
              if (subsection.title === "Water") acc.water = subsection.totalScore;
              if (subsection.title === "Waste") acc.waste = subsection.totalScore;
              if (subsection.title === "Energy") acc.energy = subsection.totalScore;
              if (subsection.title === "Product Stewardship") acc.productStewardship = subsection.totalScore;
              return acc;
            }, {water: 0, waste: 0, energy: 0, productStewardship: 0});
          }
          return {water: null, waste: null, energy: null, productStewardship: null};
        };

        // Extract and compute necessary fields
        const environment = extractEnvironmentData("Environmental Framework");
        const environmentOverall = extractSectionData("Environmental Framework");
        const supplierSustainability = extractSectionData("Sustainability Ambassadorship Framework");
        const socialStewardship = extractSectionData("Social Stewardship Framework");
        const healthSafety = extractSectionData("Occupational Health & Safety Framework");
        const legalCompliances = extractSectionData("Legal Compliances");
        const governanceFramework = extractSectionData("Governance Framework");

        // Compute derived fields
        const social = (supplierSustainability?.totalScore || 0) + (socialStewardship?.totalScore || 0) + (healthSafety?.totalScore || 0);
        const governance = (legalCompliances?.totalScore || 0) + (governanceFramework?.totalScore || 0);

        // Determine MSI Score & Rating
        const msiScore = record.auditorMSIScore || 0;
        const msiRating = getMsiRating(msiScore);

        // Formatting dates
        const formatDate = (dateString: string | null) => {
          if (!dateString) return null;
          const date = new Date(dateString);
          return date.toLocaleDateString('en-GB').replace(/\//g, '.');
        };

        // Extracting vendor details
        const vendor = recordWithAssignment.vendor || {};
        const supplierAssessment = recordWithAssignment.supplierAssessmentAssignment || {};
        const auditors = supplierAssessment.group1?.assessors
          ? supplierAssessment.group1.assessors.join(', ')
          : 'Not Available';

        return {
          company_name: vendor.supplierName || 'Unknown',
          company_short_name: vendor.supplierName ? vendor.supplierName.split(' ')[0] : 'Unknown',
          spent: vendor.supplierSpentOn || 0,
          category: categoryList[vendor.supplierCategory as keyof typeof categoryList] || 'Unknown',
          group: vendor.supplierPriorityGroup || 'Unknown',
          vendor_code: parseInt(vendor.code) || null,
          audit_start_date: formatDate(supplierAssessment.auditStartDate),
          audit_end_date: formatDate(supplierAssessment.auditEndDate),
          location: vendor.supplierLocation || 'Unknown',
          water: environment.water,
          waste: environment.waste,
          energy: environment.energy,
          product_stewardship: environment.productStewardship,
          environment: environmentOverall?.totalScore || 0,
          // environment_overall: environmentOverall,
          supplier_sustainability_ambassadorship_framework: supplierSustainability?.totalScore || 0,
          social_stewardship_framework: socialStewardship?.totalScore || 0,
          health_safety: healthSafety?.totalScore || 0,
          legal_compliances: legalCompliances?.totalScore || 0,
          governance_framework: governanceFramework?.totalScore || 0,
          governance: governance,
          auditors: auditors,
          social: social,
          msi_score: msiScore,
          msi_rating: msiRating,
          status: record.status === 0 ? 'Pending' : 'Completed',
        };
      })
    );

    return responseData;
  }

  @patch('/auditor-assignment-submissions')
  @response(200, {
    description: 'AuditorAssignmentSubmission PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AuditorAssignmentSubmission, {partial: true}),
        },
      },
    })
    auditorAssignmentSubmission: AuditorAssignmentSubmission,
    @param.where(AuditorAssignmentSubmission) where?: Where<AuditorAssignmentSubmission>,
  ): Promise<Count> {
    return this.auditorAssignmentSubmissionRepository.updateAll(auditorAssignmentSubmission, where);
  }

  @get('/auditor-assignment-submissions/{id}')
  @response(200, {
    description: 'AuditorAssignmentSubmission model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(AuditorAssignmentSubmission, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.number('id') id: number,
    @param.filter(AuditorAssignmentSubmission, {exclude: 'where'}) filter?: FilterExcludingWhere<AuditorAssignmentSubmission>
  ): Promise<AuditorAssignmentSubmission> {
    return this.auditorAssignmentSubmissionRepository.findById(id, filter);
  }
  @post('/send-supplier-report-ack-mail/{id}')
  @response(204, {
    description: 'AuditorAssignmentSubmission PATCH success',
  })
  async sendSupplierReportAckMail(
    @param.path.number('id') id: number,
    @requestBody({
      required: true,
      content: {

        'application/json': {
          schema: {
            type: 'object',
            properties: {
              requestId: {type: 'number'},
            },
            required: ['requestId'],
          }
        },
      },
    }) formData: {requestId: number}
  ): Promise<any> {
    const {requestId} = formData
    let data: any = {};
    data = await this.auditorAssignmentSubmissionRepository.findById(id, {include: ['vendor']});

    if (data?.type === 2) {


      try {

        if ("vendor" in data) {
          const vendor = data.vendor;
          const roleAgainsCategory = {
            1: 25,
            2: 26,
            3: 27,
            4: 28,
            5: 29,
            6: 30,
            7: 31,
            8: 32,
            9: 33,
            10: 34,
            11: 35
          }
          const role_id = roleAgainsCategory[vendor?.supplierCategory as keyof typeof roleAgainsCategory] ?? 0;
          const roles = await this.userRoleAuthorizationRepository.execute(

            `SELECT * FROM UserRoleAuthorization
            WHERE roles IS NOT NULL
              AND JSON_LENGTH(roles) > 0
              AND JSON_CONTAINS(roles, ?, '$')`,
            [JSON.stringify([role_id])]
          )
          const headUserIds = roles.map((i: any) => i.user_id).filter((x: any) => x)
          const headUserData = await this.userProfileController.filteredUP({where: {id: {inq: headUserIds}}})
          const headSpocMailId = headUserData.map((x: any) => x.email).filter((x: any) => x)
          const score = JSON.parse(data?.response || '[]')?.reduce((acc: any, sec: any) => {
            return acc + (sec.sectionTotalScore || 0);
          }, 0);
          const message = data?.supplierAssessmentAssignmentId || ''
          const hash = CryptoJS.AES.encrypt(message.toString(), 'e!sq6kee4supassid').toString()
          const composeSubject = `Receipt of MSI Assessment Report for ${vendor?.supplierName || 'Supplier'} (${vendor.code || ''}) `


          const composeBody = `Dear TVS Team,

Thank you for sharing the MSI Assessment Report for ${vendor?.supplierName || 'Supplier'} (${vendor.code || ''}).

We acknowledge the receipt of the report and have reviewed the observations shared. We will proceed to review the detailed findings and develop the required Action Plan accordingly.

As advised, we will upload the action plan and provide status updates through the Navigos platform.

We appreciate the valuable feedback and look forward to further strengthening our sustainability practices in collaboration with the TVS team`

          const subject = `MSI Assessment Report - ${vendor?.supplierName || 'Supplier'} (Vendor Code: ${vendor.code || ''})`;
          const body = `
  <p>Dear ${vendor?.supplierName || 'Supplier'}</p>
  <p>Thank you for hosting us for the Onsite MSI Assessment.
  We truly appreciate the opportunity to visit your site, interact with your team, and exchange best practices.
  Following our assessment and your responses, please find the attached assessment report for your reference.</p>

  <strong>Rating Achieved:  ${vendor?.supplierName || 'Supplier'} ( ${vendor.code || ''}) - ${!(score != null) ? 'NA' : score >= 85 ? 'Platinum' : score > 70 ? 'Gold' : score > 55 ? 'Silver' : 'Not Met'}</strong>


<p><img src="https://api.eisqr.com/docs/1745664269282msi_score_board.jpg" width="300"/></p>

  <p>For the detailed assessment report and the calibration form with your responses, either log in to the
  <a href="https://tvsmotor-supplier.eisqr.com"> Supplier Portal </a> using the credentials previously shared, otherwise <a href="https://tvsmotor-supplier.eisqr.com/report/supplier?token=${encodeURIComponent(hash)}"> View Report </a> here </p>

<a href="mailto:<EMAIL>,<EMAIL>?subject=${encodeURIComponent(composeSubject)}&body=${encodeURIComponent(composeBody)}"> <b>Kindly acknowledge receipt of this report by clicking this</b>
</a>

  <p><strong>Next Steps:</strong></p>
  <ul>
    <li>Review the assessment report.</li>
    <li>Prepare an Action Plan to address the observations highlighted in the report.</li>
    <li>Upload your action plan directly through the Navigos portal.</li>
  </ul>

  <p>Your proactive engagement in implementing the action plan will support continuous improvement and
  help enhance your sustainability rating in future assessments.</p>

  <p>If you have any clarifications or require assistance, please feel free to contact
  <strong><EMAIL></strong> or <strong><EMAIL></strong>.</p>

  <p>We look forward to your continued collaboration towards sustainability excellence.</p>
   <p style='font-style:italic'>This is an automated message. Please do not respond to this mail</p>
`;

          const spocData = await this.userProfileController.filteredUP({where: {id: vendor?.userProfileId}})
          const spocmailId = typeof spocData?.[0]?.email === 'string' ? spocData?.[0]?.email : ''
          const spocCC = [...headSpocMailId, '<EMAIL>', '<EMAIL>']
          const vendorEmails = this.getUniqueValidEmails([data.vendor]).flatMap((x: any) => x.emails).filter((x: any) => x)


          return await this.sqsService.sendEmail([...vendorEmails, spocmailId].filter(y => y), subject, body, spocCC).then(async (info) => {
            const reportMailStatus = data.reportMailStatus || []
            reportMailStatus.push({userId: requestId, date: new Date().toISOString()})
            await this.auditorAssignmentSubmissionRepository.updateById(id, {reportMailStatus})

            return {status: true, message: 'Report Mail sent successfully'};
          }).catch(() => {
            return {status: false, message: 'There was an issue while attempting to send the report acknowledgement email. Please try again'};
          });

        } else {
          return {status: false, message: 'There was an issue while attempting to send the report acknowledgement email'}

        }
      } catch (e) {
        return {status: false, message: 'There was an issue while attempting to send the report acknowledgement email'}

      }

    } else if (data) {
      return {status: false, message: 'Report Not Approved'}
    } else {
      return {status: false, message: 'Report Not Found'}
    }

  }
  @patch('/auditor-assignment-submissions/{id}')
  @response(204, {
    description: 'AuditorAssignmentSubmission PATCH success',
  })
  async updateById(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(AuditorAssignmentSubmission, {partial: true}),
        },
      },
    })
    auditorAssignmentSubmission: AuditorAssignmentSubmission,
  ): Promise<void> {
    let data: any = {};
    data = await this.auditorAssignmentSubmissionRepository.findById(id, {include: ['vendor']});




    if (auditorAssignmentSubmission?.type === 2 && auditorAssignmentSubmission?.approved_on && !data?.approved_on) {

      const assignedActions = await this.supplierAssessmentAssignmentRepository
        .supplierActions(data?.supplierAssessmentAssignmentId)
        .find();

      const sortedActions = assignedActions.sort((a, b) => {
        if (!a.categoryOfFinding || !b.categoryOfFinding) {
          return 0;
        }
        if (a.categoryOfFinding !== b.categoryOfFinding) {
          return a.categoryOfFinding - b.categoryOfFinding;
        }
        if (a.categoryOfFinding === 3) {
          if (!a.nonComplianceType || !b.nonComplianceType) {
            return 0;
          }
          return a.nonComplianceType - b.nonComplianceType;
        }
        return 0;
      });

      let gpCount = 1, ofiCount = 1, ncrCount = 1, ncCount = 1;
      for (const action of sortedActions) {
        action.type = 12;
        action.approved_on = DateTime.utc().toString();
        if (action.categoryOfFinding === 1) {
          action.actionId = `GP${gpCount++}`;
        } else if (action.categoryOfFinding === 2) {
          action.actionId = `OFI${ofiCount++}`;
        } else if (action.categoryOfFinding === 3) {
          if (action.nonComplianceType === 1) {
            action.actionId = `NCR${ncrCount++} Major`;
          } else if (action.nonComplianceType === 2) {
            action.actionId = `NCR${ncrCount++} Minor`;
          } else if (action.nonComplianceType === 3) {
            action.actionId = `NC${ncCount++} Minor`;
          }
        }

        // **Updating each action in the database**
        await this.supplierAssessmentAssignmentRepository.supplierActions(
          data?.supplierAssessmentAssignmentId
        ).patch(action, {id: action.id});
      }
      try {

        if ("vendor" in data) {
          const vendor = data.vendor;
          const roleAgainsCategory = {
            1: 25,
            2: 26,
            3: 27,
            4: 28,
            5: 29,
            6: 30,
            7: 31,
            8: 32,
            9: 33,
            10: 34,
            11: 35
          }
          const role_id = roleAgainsCategory[vendor?.supplierCategory as keyof typeof roleAgainsCategory] ?? 0;
          const roles = await this.userRoleAuthorizationRepository.execute(

            `SELECT * FROM UserRoleAuthorization
            WHERE roles IS NOT NULL
              AND JSON_LENGTH(roles) > 0
              AND JSON_CONTAINS(roles, ?, '$')`,
            [JSON.stringify([role_id])]
          )
          const headUserIds = roles.map((i: any) => i.user_id).filter((x: any) => x)
          const headUserData = await this.userProfileController.filteredUP({where: {id: {inq: headUserIds}}})
          const headSpocMailId = headUserData.map((x: any) => x.email).filter((x: any) => x)
          const score = JSON.parse(data?.response || '[]')?.reduce((acc: any, sec: any) => {
            return acc + (sec.sectionTotalScore || 0);
          }, 0);
          const message = data?.supplierAssessmentAssignmentId || ''
          const hash = CryptoJS.AES.encrypt(message.toString(), 'e!sq6kee4supassid').toString()
          const composeSubject = `Receipt of MSI Assessment Report for ${vendor?.supplierName || 'Supplier'} (${vendor.code || ''}) `


          const composeBody = `Dear TVS Team,

Thank you for sharing the MSI Assessment Report for ${vendor?.supplierName || 'Supplier'} (${vendor.code || ''}).

We acknowledge the receipt of the report and have reviewed the observations shared. We will proceed to review the detailed findings and develop the required Action Plan accordingly.

As advised, we will upload the action plan and provide status updates through the Navigos platform.

We appreciate the valuable feedback and look forward to further strengthening our sustainability practices in collaboration with the TVS team`

          const subject = `MSI Assessment Report - ${vendor?.supplierName || 'Supplier'} (Vendor Code: ${vendor.code || ''})`;
          const body = `
  <p>Dear ${vendor?.supplierName || 'Supplier'}</p>
  <p>Thank you for hosting us for the Onsite MSI Assessment.
  We truly appreciate the opportunity to visit your site, interact with your team, and exchange best practices.
  Following our assessment and your responses, please find the attached assessment report for your reference.</p>

  <strong>Rating Achieved:  ${vendor?.supplierName || 'Supplier'} ( ${vendor.code || ''}) - ${!(score != null) ? 'NA' : score >= 85 ? 'Platinum' : score > 70 ? 'Gold' : score > 55 ? 'Silver' : 'Not Met'}</strong>


<p><img src="https://api.eisqr.com/docs/1745664269282msi_score_board.jpg" width="300"/></p>

  <p>For the detailed assessment report and the calibration form with your responses, either log in to the
  <a href="https://tvsmotor-supplier.eisqr.com"> Supplier Portal </a> using the credentials previously shared, otherwise <a href="https://tvsmotor-supplier.eisqr.com/report/supplier?token=${encodeURIComponent(hash)}"> View Report </a> here </p>

<a href="mailto:<EMAIL>,<EMAIL>?subject=${encodeURIComponent(composeSubject)}&body=${encodeURIComponent(composeBody)}"> <b>Kindly acknowledge receipt of this report by clicking this</b>
</a>

  <p><strong>Next Steps:</strong></p>
  <ul>
    <li>Review the assessment report.</li>
    <li>Prepare an Action Plan to address the observations highlighted in the report.</li>
    <li>Upload your action plan directly through the Navigos portal.</li>
  </ul>

  <p>Your proactive engagement in implementing the action plan will support continuous improvement and
  help enhance your sustainability rating in future assessments.</p>

  <p>If you have any clarifications or require assistance, please feel free to contact
  <strong><EMAIL></strong> or <strong><EMAIL></strong>.</p>

  <p>We look forward to your continued collaboration towards sustainability excellence.</p>
   <p style='font-style:italic'>This is an automated message. Please do not respond to this mail</p>
`;

          const spocData = await this.userProfileController.filteredUP({where: {id: vendor?.userProfileId}})
          const spocmailId = typeof spocData?.[0]?.email === 'string' ? spocData?.[0]?.email : ''
          const spocCC = [...headSpocMailId, '<EMAIL>', '<EMAIL>']
          const vendorEmails = this.getUniqueValidEmails([data.vendor]).flatMap((x: any) => x.emails).filter((x: any) => x)

          await this.sqsService.sendEmail([...vendorEmails, spocmailId].filter(y => y), subject, body, spocCC)
            .then(info => console.log('Email sent:', info))
            .catch(err => console.error('Error sending email:', err));


        }
      } catch (e) {
        console.log(e, "Error in sending email");
      }

    }

    await this.auditorAssignmentSubmissionRepository.updateById(id, auditorAssignmentSubmission);

    // Send rejection email when type is 0 and reject is 1
    if (auditorAssignmentSubmission?.type === 0 && auditorAssignmentSubmission?.reject && auditorAssignmentSubmission?.rejectionComments) {
      await this.sendRejectionEmail({...data, ...auditorAssignmentSubmission});
    }

    if (auditorAssignmentSubmission?.type && auditorAssignmentSubmission?.type !== 2 && (data.type !== auditorAssignmentSubmission.type)) {
      this.sendReminderEmail({...data, ...auditorAssignmentSubmission});
    }
  }


  @put('/auditor-assignment-submissions/{id}')
  @response(204, {
    description: 'AuditorAssignmentSubmission PUT success',
  })
  async replaceById(
    @param.path.number('id') id: number,
    @requestBody() auditorAssignmentSubmission: AuditorAssignmentSubmission,
  ): Promise<void> {
    await this.auditorAssignmentSubmissionRepository.replaceById(id, auditorAssignmentSubmission);
  }

  @del('/auditor-assignment-submissions/{id}')
  @response(204, {
    description: 'AuditorAssignmentSubmission DELETE success',
  })
  async deleteById(@param.path.number('id') id: number): Promise<void> {
    await this.auditorAssignmentSubmissionRepository.deleteById(id);
  }

  @get('/audit-review-automation', {
    responses: {
      '200': {
        description: 'auto',
      },
    },
  })
  async automateReviewProcess(): Promise<any> {
    const records = await this.auditorAssignmentSubmissionRepository.find({where: {type: {inq: [1, 12, 21]}}});

    const today = DateTime.utc().startOf('day');
    let updatedCount = 0;

    for (const record of records) {
      let shouldUpdate = false;
      let updateData: any = {};

      // Case 1: If type is 1, check if today is exactly submitted date + 3 days
      if (record.type === 1 && record.modified_on) {
        const submittedDate = DateTime.fromISO(record.modified_on).startOf('day');
        const targetDate = submittedDate.plus({days: 4});

        if (today.equals(targetDate)) {
          // Send reminder email before updating

          await this.sendReminderEmail(record);

          updateData = {
            type: 21,
            auto_reviewed: true,
            auto_reviewed_on: DateTime.utc().toISO()
          };
          shouldUpdate = true;
        }
      }

      // Case 2: If type is 21, check if today is exactly reference date + 3 days
      else if (record.type === 21) {
        let referenceDate: DateTime | null = null;

        // Use auto_reviewed_on if auto_reviewed is true, otherwise use reviewed_on
        if (record.auto_reviewed === true && record.auto_reviewed_on) {
          referenceDate = DateTime.fromISO(record.auto_reviewed_on).startOf('day');
        } else if (record.reviewed_on) {
          referenceDate = DateTime.fromISO(record.reviewed_on).startOf('day');
        }

        if (referenceDate) {
          const targetDate = referenceDate.plus({days: 4});

          if (today.equals(targetDate)) {
            updateData = {
              auto_second_review: true,
              auto_second_review_on: DateTime.utc().toISO(),
              type: 12
            };
            shouldUpdate = true;
          }
        }
      }

      // Update the record if conditions are met
      if (shouldUpdate) {
        await this.auditorAssignmentSubmissionRepository.updateById(record.id, updateData);
        updatedCount++;
      }
    }

    return {
      message: `Processed ${records.length} records, updated ${updatedCount} records`,
      processedCount: records.length,
      updatedCount: updatedCount
    };
  }


  getUniqueValidEmails(data: any) {
    const seenEmails = new Set(); // Global tracker for unique emails

    return data.map(({code, supplierEmail3, supplierEmail2}: any) => {
      const uniqueEmails = new Set(); // Local tracker to prevent duplicate emails within the same code

      [supplierEmail3, supplierEmail2].forEach((email: any) => {
        if (this.isValidEmail(email) && !seenEmails.has(email)) {
          uniqueEmails.add(email);
          seenEmails.add(email); // Track globally
        }
      });

      return {code, emails: [...uniqueEmails]};
    }).filter((entry: any) => entry.emails.length > 0); // Remove empty email lists
  }

  isValidEmail(email: any) {
    if (!email?.trim()) return false; // Returns false for null, undefined, or empty string
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
  };

  private async sendReminderEmail(record: any): Promise<void> {
    try {
      // Get supplier assessment assignment with vendor relation
      const assignment = await this.supplierAssessmentAssignmentRepository.findById(
        record.supplierAssessmentAssignmentId,
        {include: [{relation: 'vendor'}]}
      );

      if (!assignment) {
        console.warn(`No assignment found for record ${record.id}`);
        return;
      }

      const supplierName = (assignment as any).vendor?.supplierName || 'Unknown Supplier';
      const auditorName = 'MSI Assessor'

      // Calculate due date (current date + 3 days)
      const dueDate = DateTime.utc().plus({days: 3});
      const dueDateFormatted = dueDate.toFormat('dd MMM yyyy');

      // Get reviewer emails based on record type
      const reviewerEmails = await this.getReviewerEmails(record, assignment);

      if (reviewerEmails.length === 0) {
        console.warn(`No reviewer emails found for record ${record.id}`);
        return;
      }

      const subject = `Notification : MSI Calibration Audit Report Submitted – ${supplierName}`;

      const emailBody = `
        <div>
          <p>Dear ${(record.type === 1 || record.type === 21) ? "Reviewer" : "Approver"}</p>

          <p>This is to notify you that the MSI Calibration Audit Report for <strong>${supplierName}</strong> has been submitted by <strong>${auditorName}</strong> and is now pending for your ${(record.type === 1 || record.type === 21) ? "review" : "approve"}.</p>

          <p>Please log in to the <a href="https://tvsmotor.eisqr.com"> EiSqr – Navigos</a> to ${(record.type === 1 || record.type === 21) ? "review" : "approve"} the submitted report, including the audit findings and supporting documentation, by <strong>${dueDateFormatted}</strong>.</p>

          <p>Your ${(record.type === 1 || record.type === 21) ? "review" : "approve"} and timely action are essential to ensure smooth progress through the calibration audit workflow.</p>

          <p>For any queries or assistance, please reach out to <NAME_EMAIL></p>

          <p>Best Regards</p>
        </div>
      `;

      // Send email using SQS service
      await this.sqsService.sendEmail(reviewerEmails, subject, emailBody, ['<EMAIL>', '<EMAIL>']);

      console.log(`Reminder email sent for record ${record.id} to ${reviewerEmails.length} reviewers`);

    } catch (error) {
      console.error(`Error sending reminder email for record ${record.id}:`, error);
    }
  }

  private async getReviewerEmails(record: any, assignment: any): Promise<string[]> {
    try {
      let reviewerIds: number[] = [];

      if (record.type === 1) {
        // For type 1: Get reviewer_ids from SupplierAssessmentAssignment
        reviewerIds = assignment.reviewer_ids || [];
      } else if (record.type === 21) {
        // For type 21: Get users with role 37 from UserRoleAuthorization
        const roleUsers = await this.userRoleAuthorizationRepository.execute(
          `SELECT * FROM UserRoleAuthorization
           WHERE roles IS NOT NULL
             AND JSON_LENGTH(roles) > 0
             AND JSON_CONTAINS(roles, ?, '$')`,
          [JSON.stringify(18)]
        );
        reviewerIds = roleUsers.map((user: any) => user.user_id).filter((id: any) => id);
      } else if (record.type === 12) {
        // For type 21: Get users with role 37 from UserRoleAuthorization
        const roleUsers = await this.userRoleAuthorizationRepository.execute(
          `SELECT * FROM UserRoleAuthorization
           WHERE roles IS NOT NULL
             AND JSON_LENGTH(roles) > 0
             AND JSON_CONTAINS(roles, ?, '$')`,
          [JSON.stringify(37)]
        );
        reviewerIds = roleUsers.map((user: any) => user.user_id).filter((id: any) => id);
      }

      if (reviewerIds.length === 0) {
        return [];
      }

      // Get user profiles and extract email addresses
      const reviewers = await this.userProfileController.filteredUP({
        where: {id: {inq: reviewerIds}}
      });

      return reviewers
        .map((reviewer: any) => reviewer.email)
        .filter((email: any) => email && this.isValidEmail(email));

    } catch (error) {
      console.error(`Error fetching reviewer emails for record ${record.id}:`, error);
      return [];
    }
  }

  /**
   * Send rejection email to auditor when report is rejected
   */
  private async sendRejectionEmail(record: any): Promise<void> {
    try {
      // Get supplier assessment assignment with vendor relation
      const assignment = await this.supplierAssessmentAssignmentRepository.findById(
        record.supplierAssessmentAssignmentId,
        {include: [{relation: 'vendor'}]}
      );

      if (!assignment) {
        console.warn(`No assignment found for record ${record.id}`);
        return;
      }

      const supplierName = (assignment as any).vendor?.supplierName || 'Unknown Supplier';
      const supplierCode = (assignment as any).vendor?.code || '';

      // Get auditor emails (group1 to group4 from supplierAssessmentAssignmentId)
      const auditorEmails = await this.getAuditorEmails(assignment);

      // Debug logging for group assessors
      console.log(`Assignment ${assignment.id} group assessors:`, {
        group1_assessors: assignment.group1?.assessors || null,
        group2_assessors: assignment.group2?.assessors || null,
        group3_assessors: assignment.group3?.assessors || null,
        group4_assessors: assignment.group4?.assessors || null
      });

      // Get reviewer emails (reviewer_ids from supplierAssessmentAssignmentId)
      const reviewer1Emails = await this.getReviewer1Emails(assignment);

      // Get reviewer2 emails (role 18)
      const reviewer2Emails = await this.getReviewer2Emails();

      // Get section admin emails (role 12)
      const sectionAdminEmails = await this.getSectionAdminEmails();

      if (auditorEmails.length === 0) {
        console.warn(`No assessor emails found for record ${record.id}. Assignment ID: ${record.supplierAssessmentAssignmentId}. Groups may be null/empty or assessors arrays may contain no valid IDs.`);
        return;
      }

      // Combine all CC emails
      const ccEmails = [
        ...reviewer1Emails,
        ...reviewer2Emails,
        ...sectionAdminEmails
      ].filter((email, index, self) => email && self.indexOf(email) === index); // Remove duplicates

      const subject = `Action Required: Resubmission of Supplier Calibration Report – ${supplierName} (${supplierCode})`;

      const body = `
        <div style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
          <p>Dear Auditor,</p>

          <p>This is to inform you that the Calibration Audit Report submitted for <strong>${supplierName}</strong> has been reviewed and rejected by the assigned Reviewer on the Navigos Sustainability Platform.</p>

          <p>We request you to kindly re-examine the feedback provided by the Reviewer and revise the report accordingly. Once the necessary corrections have been made, please resubmit the updated Calibration Report on the platform at the earliest.</p>

          <p>Accurate and complete reporting is essential for ensuring the integrity of the audit process and driving meaningful supplier improvements.</p>

          <p>Should you need any support or clarification, please feel free to contact us at <a href="mailto:<EMAIL>"><EMAIL></a>, copying <EMAIL></p>

          <p>Thank you for your attention to this matter.</p>

          <p>Warm regards,<br>
          TVS Motor Company Limited</p>

        </div>
      `;


      await this.sqsService.sendEmail(auditorEmails, subject, body, ccEmails);
    } catch (error) {
      console.error(`Error sending rejection email for record ${record.id}:`, error);
    }
  }

  /**
   * Get auditor emails from group1 to group4 assessors in SupplierAssessmentAssignment
   */
  private async getAuditorEmails(assignment: any): Promise<string[]> {
    try {
      const auditorIds: number[] = [];

      // Collect assessor IDs from group1 to group4, handling the correct object structure
      ['group1', 'group2', 'group3', 'group4'].forEach(groupName => {
        const group = assignment[groupName];

        // Check if group exists and has assessors array
        if (group && typeof group === 'object' && group.assessors && Array.isArray(group.assessors) && group.assessors.length > 0) {
          // Filter out null, undefined, and invalid IDs from assessors array
          const validIds = group.assessors.filter((id: any) =>
            id !== null && id !== undefined && typeof id === 'number' && id > 0
          );
          auditorIds.push(...validIds);
        }
      });

      // Remove duplicates
      const uniqueAuditorIds = [...new Set(auditorIds)];

      if (uniqueAuditorIds.length === 0) {
        console.warn(`No valid assessor IDs found in groups for assignment ${assignment.id}`);
        return [];
      }

      // Get user profiles and extract email addresses
      const auditors = await this.userProfileController.filteredUP({
        where: {id: {inq: uniqueAuditorIds}}
      });

      const validEmails = auditors
        .map((auditor: any) => auditor.email)
        .filter((email: any) => email && this.isValidEmail(email));

      if (validEmails.length === 0) {
        console.warn(`No valid emails found for assessors in assignment ${assignment.id}`);
      }

      return validEmails;

    } catch (error) {
      console.error(`Error fetching assessor emails for assignment ${assignment?.id}:`, error);
      return [];
    }
  }

  /**
   * Get reviewer1 emails from reviewer_ids in SupplierAssessmentAssignment
   */
  private async getReviewer1Emails(assignment: any): Promise<string[]> {
    try {
      const reviewerIds = assignment?.reviewer_ids || [];

      if (reviewerIds.length === 0) {
        return [];
      }

      // Get user profiles and extract email addresses
      const reviewers = await this.userProfileController.filteredUP({
        where: {id: {inq: reviewerIds}}
      });

      return reviewers
        .map((reviewer: any) => reviewer.email)
        .filter((email: any) => email && this.isValidEmail(email));

    } catch (error) {
      console.error(`Error fetching reviewer1 emails:`, error);
      return [];
    }
  }

  /**
   * Get reviewer2 emails based on role 18
   */
  private async getReviewer2Emails(): Promise<string[]> {
    try {
      const roleUsers = await this.userRoleAuthorizationRepository.execute(
        `SELECT * FROM UserRoleAuthorization
         WHERE roles IS NOT NULL
           AND JSON_LENGTH(roles) > 0
           AND JSON_CONTAINS(roles, ?, '$')`,
        [JSON.stringify([18])]
      );

      const reviewerIds = roleUsers.map((user: any) => user.user_id).filter((id: any) => id);

      if (reviewerIds.length === 0) {
        return [];
      }

      // Get user profiles and extract email addresses
      const reviewers = await this.userProfileController.filteredUP({
        where: {id: {inq: reviewerIds}}
      });

      return reviewers
        .map((reviewer: any) => reviewer.email)
        .filter((email: any) => email && this.isValidEmail(email));

    } catch (error) {
      console.error(`Error fetching reviewer2 emails:`, error);
      return [];
    }
  }

  /**
   * Get section admin emails based on role 12
   */
  private async getSectionAdminEmails(): Promise<string[]> {
    try {
      const roleUsers = await this.userRoleAuthorizationRepository.execute(
        `SELECT * FROM UserRoleAuthorization
         WHERE roles IS NOT NULL
           AND JSON_LENGTH(roles) > 0
           AND JSON_CONTAINS(roles, ?, '$')`,
        [JSON.stringify([12])]
      );

      const adminIds = roleUsers.map((user: any) => user.user_id).filter((id: any) => id);

      if (adminIds.length === 0) {
        return [];
      }

      // Get user profiles and extract email addresses
      const admins = await this.userProfileController.filteredUP({
        where: {id: {inq: adminIds}}
      });

      return admins
        .map((admin: any) => admin.email)
        .filter((email: any) => email && this.isValidEmail(email));

    } catch (error) {
      console.error(`Error fetching section admin emails:`, error);
      return [];
    }
  }
}
